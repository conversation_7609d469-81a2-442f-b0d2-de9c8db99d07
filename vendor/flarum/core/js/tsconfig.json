// NOTE:
// If you're looking at core's `tsconfig.json` for the "perfect"
// tsconfig for your extension, look at the dedicated tsconfig
// repo instead.
//
// https://github.com/flarum/flarum-tsconfig

{
  // Use Flarum's tsconfig as a starting point
  "extends": "flarum-tsconfig",
  // This will match all .ts, .tsx, .d.ts, .js, .jsx files
  "include": ["src/**/*", "@types/**/*"],
  "compilerOptions": {
    // This will output typings to `dist-typings`
    // If you change this, also change the `package.json` clean script
    "declarationDir": "./dist-typings"
  }
}
