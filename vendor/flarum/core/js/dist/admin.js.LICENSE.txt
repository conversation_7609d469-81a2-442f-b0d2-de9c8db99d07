/*!
                                           * quantize.js Copyright 2008 <PERSON>.
                                           * Licensed under the MIT license: http://www.opensource.org/licenses/mit-license.php
                                           */

/*!
 * Block below copied from Protovis: http://mbostock.github.com/protovis/
 * Copyright 2010 Stanford Visualization Group
 * Licensed under the BSD License: http://www.opensource.org/licenses/bsd-license.php
 */

/*!
 * Color Thief v2.0
 * by <PERSON><PERSON> - http://www.lokeshdhakar.com
 *
 * Thanks
 * ------
 * <PERSON> - For creating quantize.js.
 * <PERSON> - For clean up and optimization. @JFSIII
 * <PERSON> - For adding drag and drop support to the demo page.
 *
 * License
 * -------
 * Copyright 2011, 2015 <PERSON><PERSON>
 * Released under the MIT license
 * https://raw.githubusercontent.com/lokesh/color-thief/master/LICENSE
 *
 */

/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */

/*!
* focus-trap 6.9.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/

/*!
* tabbable 5.3.3
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
