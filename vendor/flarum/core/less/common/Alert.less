.Alert {
  padding: 12px 16px;
  border-radius: var(--border-radius);
  line-height: 1.5;

  background: var(--alert-bg);

  &,
  a,
  a:hover,
  .Button,
  .Button:hover,
  .Button:active,
  .Button.active,
  .Button:focus,
  .Button.focus {
    color: var(--alert-color);
  }
}
.Alert--error {
  .Alert--color(var(--alert-error-color), var(--alert-error-bg));
}
.Alert--success {
  .Alert--color(var(--alert-success-color), var(--alert-success-bg));

  a, a:hover {
    text-decoration: underline;
  }
}
.Alert--color(@color; @background) {
  --alert-bg: @background;
  --alert-color: @color;
}
.Alert-controls {
  list-style-type: none;
  padding: 0;
  margin: 0 -8px 0 8px;
  display: inline-block;

  > li {
    display: inline-block;
    margin: 0 5px;

    > a, > .<PERSON><PERSON> {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: bold;

      &.disabled {
        cursor: default;
        text-decoration: none;
        opacity: 0.5;
      }
      &:hover {
        text-decoration: underline;
      }
    }

    > .<PERSON><PERSON> {
      margin: -10px;
      vertical-align: 0;
    }
  }

  &:empty {
    display: none;
  }
}
.Alert-title {
  margin-bottom: 8px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}
