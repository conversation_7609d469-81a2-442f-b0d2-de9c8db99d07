.ExtensionsWidget {
  background-color: var(--body-bg);
  padding: 0;
}

.ExtensionsWidget-list {
  padding: 0;
  background-color: var(--body-bg);

  .ExtensionGroup {
    margin-bottom: 20px;

    h3 {
      color: var(--muted-color);
      text-transform: uppercase;
      font-size: 12px;
      margin: 0 0 10px;
    }
  }

  .ExtensionList {
    padding: 0;
    list-style: none;
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fit, 90px);
    margin-bottom: 0;

    > li {
      text-align: left;
      position: relative;
      display: block;
    }
  }
}

.ExtensionList-Category {
  background: var(--control-bg);
  padding: 20px 0 20px 20px;
  margin-bottom: 20px;
  border-radius: var(--border-radius);
}

.ExtensionList-Label {
  margin-top: 0;
  color: var(--muted-color);
}

.ExtensionListItem.disabled {
  .ExtensionListItem-title {
    opacity: 0.5;
    color: var(--muted-color);
  }

  .ExtensionListItem-icon {
    opacity: 0.5;
  }
}

.ExtensionListItem {
  transition: .15s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }

  a:hover {
    text-decoration: none;
  }
}

.ExtensionListItem-title {
  display: block;
  text-align: center;
  margin-top: 5px;
  color: var(--text-color);
}

.ExtensionIcon {
  --size: 90px;
  width: var(--size);
  height: var(--size);
  background: var(--control-bg);
  color: var(--control-color);
  border-radius: 6px;
  display: inline-flex;
  font-size: calc(~"var(--size) / 2");
  text-align: center;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}
