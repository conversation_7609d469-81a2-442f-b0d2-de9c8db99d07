declare const _default: {
    'tags/utils/sortTags': typeof sortTags;
    'tags/models/Tag': typeof Tag;
    'tags/helpers/tagsLabel': typeof tagsLabel;
    'tags/helpers/tagIcon': typeof tagIcon;
    'tags/helpers/tagLabel': typeof tagLabel;
    'tags/components/TagSelectionModal': typeof TagSelectionModal;
    'tags/states/TagListState': typeof TagListState;
};
export default _default;
import sortTags from "./utils/sortTags";
import Tag from "./models/Tag";
import tagsLabel from "./helpers/tagsLabel";
import tagIcon from "./helpers/tagIcon";
import tagLabel from "./helpers/tagLabel";
import TagSelectionModal from "./components/TagSelectionModal";
import TagListState from "./states/TagListState";
