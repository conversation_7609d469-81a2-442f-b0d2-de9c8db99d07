/*! For license information please see forum.js.LICENSE.txt */
(()=>{var t={297:(t,e,n)=>{var r=n(570)();t.exports=r;try{regeneratorRuntime=r}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},361:()=>{},433:t=>{function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},570:(t,e,n)=>{var r=n(433).default;function a(){"use strict";t.exports=a=function(){return n},t.exports.__esModule=!0,t.exports.default=t.exports;var e,n={},o=Object.prototype,s=o.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",m=c.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(e){d=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var a=e&&e.prototype instanceof T?e:T,o=Object.create(a.prototype),s=new E(r||[]);return i(o,"_invoke",{value:C(t,n,s)}),o}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}n.wrap=f;var h="suspendedStart",g="suspendedYield",v="executing",y="completed",b={};function T(){}function x(){}function w(){}var _={};d(_,l,(function(){return this}));var N=Object.getPrototypeOf,L=N&&N(N(O([])));L&&L!==o&&s.call(L,l)&&(_=L);var I=w.prototype=T.prototype=Object.create(_);function P(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function n(a,o,i,c){var l=p(t[a],t,o);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==r(m)&&s.call(m,"__await")?e.resolve(m.__await).then((function(t){n("next",t,i,c)}),(function(t){n("throw",t,i,c)})):e.resolve(m).then((function(t){u.value=t,i(u)}),(function(t){return n("throw",t,i,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,a){n(t,r,e,a)}))}return a=a?a.then(o,o):o()}})}function C(t,n,r){var a=h;return function(o,s){if(a===v)throw Error("Generator is already running");if(a===y){if("throw"===o)throw s;return{value:e,done:!0}}for(r.method=o,r.arg=s;;){var i=r.delegate;if(i){var c=k(i,r);if(c){if(c===b)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=v;var l=p(t,n,r);if("normal"===l.type){if(a=r.done?y:g,l.arg===b)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=y,r.method="throw",r.arg=l.arg)}}}function k(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,k(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var o=p(a,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,b;var s=o.arg;return s?s.done?(n[t.resultName]=s.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,b):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function O(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(s.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(r(t)+" is not iterable")}return x.prototype=w,i(I,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:x,configurable:!0}),x.displayName=d(w,m,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,m,"GeneratorFunction")),t.prototype=Object.create(I),t},n.awrap=function(t){return{__await:t}},P(S.prototype),d(S.prototype,u,(function(){return this})),n.AsyncIterator=S,n.async=function(t,e,r,a,o){void 0===o&&(o=Promise);var s=new S(f(t,e,r,a),o);return n.isGeneratorFunction(e)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},P(I),d(I,m,"Generator"),d(I,l,(function(){return this})),d(I,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},n.values=O,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&s.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,a){return i.type="throw",i.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=s.call(o,"catchLoc"),l=s.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&s.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,b):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),D(n),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;D(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:O(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),b}},n}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports},627:(t,e,n)=>{"use strict";n.d(e,{X:()=>Ut});const r=flarum.core.compat["forum/app"];var a=n.n(r);function o(t,e,n,r,a,o,s){try{var i=t[o](s),c=i.value}catch(t){return void n(t)}i.done?e(c):Promise.resolve(c).then(r,a)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var s=t.apply(e,n);function i(t){o(s,r,a,i,c,"next",t)}function c(t){o(s,r,a,i,c,"throw",t)}i(void 0)}))}}var i=n(297),c=n.n(i);const l=flarum.core.compat["common/app"];var u=n.n(l),d=function(){function t(){this.loadedIncludes=void 0}var e=t.prototype;return e.load=function(){var t=s(c().mark((function t(e){var n,r=this;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===e&&(e=[]),this.loadedIncludes){t.next=3;break}return t.abrupt("return",this.query(e));case 3:if(0!==(n=e.filter((function(t){return!r.loadedIncludes.has(t)}))).length){t.next=6;break}return t.abrupt("return",Promise.resolve(u().store.all("tags")));case 6:return t.abrupt("return",this.query(n));case 7:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),e.query=function(){var t=s(c().mark((function t(e){var n=this;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===e&&(e=[]),null!=this.loadedIncludes||(this.loadedIncludes=new Set),t.abrupt("return",u().store.find("tags",{include:e.join(",")}).then((function(t){return e.forEach((function(t){return n.loadedIncludes.add(t)})),t})));case 3:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),t}();const f=flarum.core.compat["common/extend"],p=flarum.core.compat["forum/components/IndexPage"];var h=n.n(p);const g=flarum.core.compat["common/components/Separator"];var v=n.n(g);const y=flarum.core.compat["common/components/LinkButton"];var b=n.n(y);function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function x(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,T(t,e)}const w=flarum.core.compat["common/components/Link"];var _=n.n(w);const N=flarum.core.compat["common/utils/classList"];var L=n.n(N);function I(t,e,n){void 0===e&&(e={}),void 0===n&&(n={});var r=t&&t.icon(),a=n.useColor,o=void 0===a||a;return e.className=L()([e.className,"icon",r?t.icon():"TagIcon"]),t&&o?(e.style=e.style||{},e.style["--color"]=t.color(),r&&(e.style.color=t.color())):t||(e.className+=" untagged"),r?m("i",e):m("span",e)}var P=function(t){function e(){return t.apply(this,arguments)||this}return x(e,t),e.prototype.view=function(t){var e=this.attrs.model,n=e&&e.description(),r=L()("TagLinkButton hasIcon",{child:e.isChild()},this.attrs.className);return m(_(),{className:r,href:this.attrs.route,style:e?{"--color":e.color()}:void 0,title:n||void 0},I(e,{className:"Button-icon"}),m("span",{className:"Button-label"},e?e.name():app.translator.trans("flarum-tags.forum.index.untagged_link")))},e.initAttrs=function(e){t.initAttrs.call(this,e);var n=e.model;e.params.tags=n?n.slug():"untagged",e.route=app.route("tag",e.params)},e}(b());const S=flarum.core.compat["common/components/Page"];var C=n.n(S);const k=flarum.core.compat["common/components/LoadingIndicator"];var A=n.n(k);const D=flarum.core.compat["common/helpers/listItems"];var E=n.n(D);const O=flarum.core.compat["common/utils/ItemList"];var M=n.n(O);const R=flarum.core.compat["common/helpers/humanTime"];var j=n.n(R);const q=flarum.core.compat["common/helpers/textContrastClass"];var B=n.n(q);const H=flarum.core.compat["common/utils/extract"];var F=n.n(H);function G(t,e){void 0===e&&(e={}),e.style=e.style||{},e.className="TagLabel "+(e.className||"");var n=F()(e,"link"),r=t?t.name():app.translator.trans("flarum-tags.lib.deleted_tag_text");if(t){var a=t.color();a&&(e.style["--tag-bg"]=a,e.className=L()(e.className,"colored",B()(a))),n&&(e.title=t.description()||"",e.href=app.route("tag",{tags:t.slug()})),t.isChild()&&(e.className+=" TagLabel--child")}else e.className+=" untagged";return m(n?_():"span",e,m("span",{className:"TagLabel-text"},t&&t.icon()&&I(t,{className:"TagLabel-icon"},{useColor:!1}),m("span",{className:"TagLabel-name"},r)))}function V(t){return t.slice(0).sort((function(t,e){var n=t.position(),r=e.position();if(null===n&&null===r)return e.discussionCount()-t.discussionCount();if(null===r)return-1;if(null===n)return 1;var a=t.parent(),o=e.parent();return a===o?n-r:a&&o?a.position()-o.position():a?a===e?1:a.position()-r:o?o===t?-1:n-o.position():0}))}var K=function(t){function e(){return t.apply(this,arguments)||this}x(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),app.history.push("tags",app.translator.trans("flarum-tags.forum.header.back_to_tags_tooltip")),this.tags=[];var r=app.preloadedApiDocument();r?this.tags=V(r.filter((function(t){return!t.isChild()}))):(this.loading=!0,app.tagList.load(["children","lastPostedDiscussion","parent"]).then((function(){n.tags=V(app.store.all("tags").filter((function(t){return!t.isChild()}))),n.loading=!1,m.redraw()})))},n.oncreate=function(e){t.prototype.oncreate.call(this,e),app.setTitle(app.translator.trans("flarum-tags.forum.all_tags.meta_title_text")),app.setTitleCount(0)},n.view=function(){return m("div",{className:"TagsPage"},this.pageContent().toArray())},n.pageContent=function(){var t=new(M());return t.add("hero",this.hero(),100),t.add("main",m("div",{className:"container"},this.mainContent().toArray()),10),t},n.mainContent=function(){var t=new(M());return t.add("sidebar",this.sidebar(),100),t.add("content",this.content(),10),t},n.content=function(){return m("div",{className:"TagsPage-content sideNavOffset"},this.contentItems().toArray())},n.contentItems=function(){var t=new(M());if(this.loading)t.add("loading",m(A(),null));else{var e=this.tags.filter((function(t){return null!==t.position()})),n=this.tags.filter((function(t){return null===t.position()}));t.add("tagTiles",this.tagTileListView(e),100),n.length&&t.add("cloud",this.cloudView(n),10)}return t},n.hero=function(){return h().prototype.hero()},n.sidebar=function(){return m("nav",{className:"TagsPage-nav IndexPage-nav sideNav"},m("ul",null,E()(this.sidebarItems().toArray())))},n.sidebarItems=function(){return h().prototype.sidebarItems()},n.tagTileListView=function(t){return m("ul",{className:"TagTiles"},t.map(this.tagTileView.bind(this)))},n.tagTileView=function(t){var e=t.lastPostedDiscussion(),n=V(t.children()||[]);return m("li",{className:L()("TagTile",{colored:t.color()},B()(t.color())),style:{"--tag-bg":t.color()}},m(_(),{className:"TagTile-info",href:app.route.tag(t)},t.icon()&&I(t,{},{useColor:!1}),m("h3",{className:"TagTile-name"},t.name()),m("p",{className:"TagTile-description"},t.description()),!!n&&m("div",{className:"TagTile-children"},n.map((function(t){return[m(_(),{href:app.route.tag(t)},t.name())," "]})))),e?m(_(),{className:"TagTile-lastPostedDiscussion",href:app.route.discussion(e,e.lastPostNumber())},m("span",{className:"TagTile-lastPostedDiscussion-title"},e.title()),j()(e.lastPostedAt())):m("span",{className:"TagTile-lastPostedDiscussion"}))},n.cloudView=function(t){return m("div",{className:"TagCloud"},t.map((function(t){return[G(t,{link:!0})," "]})))},e}(C());function $(){(0,f.extend)(h().prototype,"navItems",(function(t){if(t.add("tags",m(b(),{icon:"fas fa-th-large",href:a().route("tags")},a().translator.trans("flarum-tags.forum.index.tags_link")),-10),!a().current.matches(K)){t.add("separator",m(v(),null),-12);var e=a().search.stickyParams(),n=a().store.all("tags"),r=this.currentTag(),o=function(n){var a=r===n;!a&&r&&(a=r.parent()===n),t.add("tag"+n.id(),m(P,{model:n,params:e,active:a},null==n?void 0:n.name()),-14)};V(n).filter((function(t){return null!==t.position()&&(!t.isChild()||r&&(t.parent()===r||t.parent()===r.parent()))})).forEach(o);var s=n.filter((function(t){return null===t.position()})).sort((function(t,e){return e.discussionCount()-t.discussionCount()}));s.splice(0,3).forEach(o),s.length&&t.add("moreTags",m(b(),{href:a().route("tags")},a().translator.trans("flarum-tags.forum.index.more_link")),-16)}}))}const U=flarum.core.compat["forum/states/DiscussionListState"];var X=n.n(U);const Y=flarum.core.compat["forum/states/GlobalSearchState"];var z=n.n(Y);const J=flarum.core.compat["common/Component"];var Q=n.n(J),W=function(t){function e(){return t.apply(this,arguments)||this}x(e,t);var n=e.prototype;return n.view=function(){var t,e=this.attrs.model.color();return m("header",{className:L()("Hero","TagHero",(t={"TagHero--colored":e},t[B()(e)]=e,t)),style:e?{"--hero-bg":e}:void 0},m("div",{className:"container"},this.viewItems().toArray()))},n.viewItems=function(){var t=new(M());return t.add("content",m("div",{className:"containerNarrow"},this.contentItems().toArray()),80),t},n.contentItems=function(){var t=new(M()),e=this.attrs.model;return t.add("tag-title",m("h1",{className:"Hero-title"},e.icon()&&I(e,{},{useColor:!1})," ",e.name()),100),t.add("tag-subtitle",m("div",{className:"Hero-subtitle"},e.description()),90),t},e}(Q()),Z=function(t){return a().store.all("tags").find((function(e){return 0===e.slug().localeCompare(t,void 0,{sensitivity:"base"})}))};function tt(){h().prototype.currentTag=function(){var t=this;if(this.currentActiveTag)return this.currentActiveTag;var e=a().search.params().tags,n=null;if(e&&(n=Z(e)),e&&!n||n&&!n.isChild()&&!n.children()){if(this.currentTagLoading)return;this.currentTagLoading=!0,a().store.find("tags",e,{include:"children,children.parent,parent,state"}).then((function(){t.currentActiveTag=Z(e),m.redraw()})).finally((function(){t.currentTagLoading=!1}))}return n?(this.currentActiveTag=n,this.currentActiveTag):void 0},(0,f.override)(h().prototype,"hero",(function(t){var e=this.currentTag();return e?m(W,{model:e}):t()})),(0,f.extend)(h().prototype,"view",(function(t){var e=this.currentTag();e&&(t.attrs.className+=" IndexPage--tag"+e.id())})),(0,f.extend)(h().prototype,"setTitle",(function(){var t=this.currentTag();t&&a().setTitle(t.name())})),(0,f.extend)(h().prototype,"sidebarItems",(function(t){var e=this.currentTag();if(e){var n=e.color(),r=e.canStartDiscussion()||!a().session.user,o=t.get("newDiscussion");n&&(o.attrs.className=L()([o.attrs.className,"Button--tagColored",B()(n)]),o.attrs.style={"--color":n}),o.attrs.disabled=!r,o.children=a().translator.trans(r?"core.forum.index.start_discussion_button":"core.forum.index.cannot_start_discussion_button")}})),(0,f.extend)(z().prototype,"params",(function(t){t.tags=m.route.param("tags")})),(0,f.extend)(X().prototype,"requestParams",(function(t){var e;if("string"==typeof t.include?t.include=[t.include]:null==(e=t.include)||e.push("tags","tags.parent"),this.params.tags){var n,r=null!=(n=t.filter)?n:{};r.tag=this.params.tags;var a=r.q;a&&(r.q=a+" tag:"+this.params.tags),t.filter=r}}))}const et=flarum.core.compat["forum/components/DiscussionListItem"];var nt=n.n(et);const rt=flarum.core.compat["forum/components/DiscussionHero"];var at=n.n(rt);function ot(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}var st=["link"];function it(t,e){void 0===e&&(e={});var n=[],r=e,a=r.link,o=ot(r,st);return o.className=L()("TagsLabel",o.className),t?V(t).forEach((function(e){(e||1===t.length)&&n.push(G(e,{link:a}))})):n.push(G()),m("span",o,n)}function ct(){(0,f.extend)(nt().prototype,"infoItems",(function(t){var e=this.attrs.discussion.tags();e&&e.length&&t.add("tags",it(e),10)})),(0,f.extend)(at().prototype,"view",(function(t){var e=V(this.attrs.discussion.tags());if(e&&e.length){var n=e[0].color();n&&(t.attrs.style={"--hero-bg":n},t.attrs.className=L()(t.attrs.className,"DiscussionHero--colored",B()(n)))}})),(0,f.extend)(at().prototype,"items",(function(t){var e=this.attrs.discussion.tags();e&&e.length&&t.add("tags",it(e,{link:!0}),5)}))}const lt=flarum.core.compat["forum/utils/DiscussionControls"];var ut=n.n(lt);const mt=flarum.core.compat["common/components/Button"];var dt=n.n(mt);const ft=flarum.core.compat["forum/components/DiscussionPage"];var pt=n.n(ft);const ht=flarum.core.compat["common/utils/extractText"];var gt=n.n(ht);function vt(t){var e=app.store.all("tags");return t?e.filter((function(e){return e.canAddToDiscussion()||-1!==t.tags().indexOf(e)})):e.filter((function(t){return t.canStartDiscussion()}))}const yt=flarum.core.compat["common/helpers/highlight"];var bt=n.n(yt);const Tt=flarum.core.compat["common/utils/KeyboardNavigatable"];var xt=n.n(Tt);const wt=flarum.core.compat["common/components/Modal"];var _t=n.n(wt);const Nt=flarum.core.compat["common/utils/Stream"];var Lt=n.n(Nt),It=["className","isToggled"],Pt=function(t){function e(){return t.apply(this,arguments)||this}return x(e,t),e.prototype.view=function(t){var e=this.attrs,n=e.className,r=e.isToggled,a=ot(e,It),o=r?"far fa-check-circle":"far fa-circle";return m(dt(),Object.assign({},a,{icon:o,className:L()([n,r&&"Button--toggled"])}),t.children)},e}(Q()),St=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=t.call.apply(t,[this].concat(r))||this).loading=!0,e.tags=void 0,e.selected=[],e.bypassReqs=!1,e.filter=Lt()(""),e.focused=!1,e.navigator=new(xt()),e.indexTag=void 0,e}x(e,t),e.initAttrs=function(e){var n,r,a,o,s,i,c,l,m,d,f,p;t.initAttrs.call(this,e),e.title||(e.title=gt()(u().translator.trans("flarum-tags.lib.tag_selection_modal.title"))),e.canSelect||(e.canSelect=function(){return!0}),null!=e.allowResetting||(e.allowResetting=!0),e.limits={min:{total:null!=(n=null==(r=e.limits)||null==(r=r.min)?void 0:r.total)?n:-1/0,primary:null!=(a=null==(o=e.limits)||null==(o=o.min)?void 0:o.primary)?a:-1/0,secondary:null!=(s=null==(i=e.limits)||null==(i=i.min)?void 0:i.secondary)?s:-1/0},max:{total:null!=(c=null==(l=e.limits)||null==(l=l.max)?void 0:l.total)?c:1/0,primary:null!=(m=null==(d=e.limits)||null==(d=d.max)?void 0:d.primary)?m:1/0,secondary:null!=(f=null==(p=e.limits)||null==(p=p.max)?void 0:p.secondary)?f:1/0}},function(t){if(t.min.primary>t.max.primary)throw new Error("The minimum number of primary tags allowed cannot be more than the maximum number of primary tags allowed.");if(t.min.secondary>t.max.secondary)throw new Error("The minimum number of secondary tags allowed cannot be more than the maximum number of secondary tags allowed.");if(t.min.total>t.max.primary+t.max.secondary)throw new Error("The minimum number of tags allowed cannot be more than the maximum number of primary and secondary tags allowed together.");if(t.max.total<t.min.primary+t.min.secondary)throw new Error("The maximum number of tags allowed cannot be less than the minimum number of primary and secondary tags allowed together.");if(t.min.total>t.max.total)throw new Error("The minimum number of tags allowed cannot be more than the maximum number of tags allowed.")}(e.limits)};var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),this.navigator.onUp((function(){return n.setIndex(n.getCurrentNumericIndex()-1,!0)})).onDown((function(){return n.setIndex(n.getCurrentNumericIndex()+1,!0)})).onSelect(this.select.bind(this)).onRemove((function(){return n.selected.splice(n.selected.length-1,1)})),u().tagList.load(["parent"]).then((function(t){n.loading=!1,n.attrs.selectableTags&&(t=n.attrs.selectableTags(t)),n.tags=V(t),n.attrs.selectedTags&&n.attrs.selectedTags.map(n.addTag.bind(n)),n.indexTag=t[0],m.redraw()}))},n.className=function(){return L()("TagSelectionModal",this.attrs.className)},n.title=function(){return this.attrs.title},n.content=function(){var t=this;if(this.loading||!this.tags)return m(A(),null);var e=this.filter().toLowerCase(),n=this.primaryCount(),r=this.secondaryCount(),a=this.getFilteredTags(),o=Math.max(gt()(this.getInstruction(n,r)).length,this.filter().length);return[m("div",{className:"Modal-body"},m("div",{className:"TagSelectionModal-form"},m("div",{className:"TagSelectionModal-form-input"},m("div",{className:"TagsInput FormControl "+(this.focused?"focus":""),onclick:function(){return t.$(".TagsInput input").focus()}},m("span",{className:"TagsInput-selected"},this.selected.map((function(e){return m("span",{className:"TagsInput-tag",onclick:function(){t.removeTag(e),t.onready()}},G(e))}))),m("input",{className:"FormControl",placeholder:gt()(this.getInstruction(n,r)),bidi:this.filter,style:{width:o+"ch"},onkeydown:this.navigator.navigate.bind(this.navigator),onfocus:function(){return t.focused=!0},onblur:function(){return t.focused=!1}}))),m("div",{className:"TagSelectionModal-form-submit App-primaryControl"},m(dt(),{type:"submit",className:"Button Button--primary",disabled:!this.meetsRequirements(n,r),icon:"fas fa-check"},u().translator.trans("flarum-tags.lib.tag_selection_modal.submit_button"))))),m("div",{className:"Modal-footer"},m("ul",{className:"TagSelectionModal-list SelectTagList"},a.map((function(n){return m("li",{"data-index":n.id(),className:L()({pinned:null!==n.position(),child:!!n.parent(),colored:!!n.color(),selected:t.selected.includes(n),active:t.indexTag===n}),style:{color:n.color()},onmouseover:function(){return t.indexTag=n},onclick:t.toggleTag.bind(t,n)},I(n),m("span",{className:"SelectTagListItem-name"},bt()(n.name(),e)),n.description()?m("span",{className:"SelectTagListItem-description"},n.description()):"")}))),this.attrs.limits.allowBypassing&&m("div",{className:"TagSelectionModal-controls"},m(Pt,{className:"Button",onclick:function(){return t.bypassReqs=!t.bypassReqs},isToggled:this.bypassReqs},u().translator.trans("flarum-tags.lib.tag_selection_modal.bypass_requirements"))))]},n.getFilteredTags=function(){var t=this,e=this.filter().toLowerCase(),n=this.primaryCount(),r=this.secondaryCount(),a=this.tags;return this.attrs.requireParentTag&&(a=a.filter((function(e){var n=e.parent();return null!==n&&(!1===n||t.selected.includes(n))}))),this.bypassReqs||(this.selected.length>=this.attrs.limits.max.total?a=a.filter((function(e){return t.selected.includes(e)})):(n>=this.attrs.limits.max.primary&&(a=a.filter((function(e){return!e.isPrimary()||t.selected.includes(e)}))),r>=this.attrs.limits.max.secondary&&(a=a.filter((function(e){return e.isPrimary()||t.selected.includes(e)}))))),e&&(a=a.filter((function(t){return t.name().toLowerCase().includes(e)}))),this.indexTag&&a.includes(this.indexTag)||(this.indexTag=a[0]),a},n.primaryCount=function(){return this.selected.filter((function(t){return t.isPrimary()})).length},n.secondaryCount=function(){return this.selected.filter((function(t){return!t.isPrimary()})).length},n.meetsRequirements=function(t,e){return!!(this.bypassReqs||this.attrs.allowResetting&&0===this.selected.length)||!(this.selected.length<this.attrs.limits.min.total)&&t>=this.attrs.limits.min.primary&&e>=this.attrs.limits.min.secondary},n.addTag=function(t){if(t&&this.attrs.canSelect(t)){if(this.attrs.onSelect&&this.attrs.onSelect(t,this.selected),this.attrs.requireParentTag){var e=t.parent();e&&!this.selected.includes(e)&&this.selected.push(e)}this.selected.includes(t)||this.selected.push(t)}},n.removeTag=function(t){var e=this.selected.indexOf(t);-1!==e&&(this.selected.splice(e,1),this.attrs.requireParentTag&&this.selected.filter((function(e){return e.parent()===t})).forEach(this.removeTag.bind(this)),this.attrs.onDeselect&&this.attrs.onDeselect(t,this.selected))},n.toggleTag=function(t){this.tags&&(this.selected.includes(t)?this.removeTag(t):this.addTag(t),this.filter()&&(this.filter(""),this.indexTag=this.tags[0]),this.onready())},n.getInstruction=function(t,e){if(this.bypassReqs)return"";if(t<this.attrs.limits.min.primary){var n=this.attrs.limits.min.primary-t;return gt()(u().translator.trans("flarum-tags.lib.tag_selection_modal.choose_primary_placeholder",{count:n}))}if(e<this.attrs.limits.min.secondary){var r=this.attrs.limits.min.secondary-e;return gt()(u().translator.trans("flarum-tags.lib.tag_selection_modal.choose_secondary_placeholder",{count:r}))}if(this.selected.length<this.attrs.limits.min.total){var a=this.attrs.limits.min.total-this.selected.length;return gt()(u().translator.trans("flarum-tags.lib.tag_selection_modal.choose_tags_placeholder",{count:a}))}return""},n.onsubmit=function(t){t.preventDefault(),this.attrs.onsubmit&&this.attrs.onsubmit(this.selected),this.hide()},n.select=function(t){t.metaKey||t.ctrlKey||this.indexTag&&this.selected.includes(this.indexTag)?this.selected.length&&this.$('button[type="submit"]').click():this.indexTag&&this.getItem(this.indexTag)[0].dispatchEvent(new Event("click"))},n.selectableItems=function(){return this.$(".TagSelectionModal-list > li")},n.getCurrentNumericIndex=function(){return this.indexTag?this.selectableItems().index(this.getItem(this.indexTag)):-1},n.getItem=function(t){return this.selectableItems().filter('[data-index="'+t.id()+'"]')},n.setIndex=function(t,e){var n=this.selectableItems(),r=n.parent();t<0?t=n.length-1:t>=n.length&&(t=0);var a=n.eq(t);if(this.indexTag=u().store.getById("tags",a.attr("data-index")),m.redraw(),e&&this.indexTag){var o,s=r.scrollTop(),i=r.offset().top,c=i+r.outerHeight(),l=a.offset().top,d=l+a.outerHeight();l<i?o=s-i+l-parseInt(r.css("padding-top"),10):d>c&&(o=s-c+d+parseInt(r.css("padding-bottom"),10)),void 0!==o&&r.stop(!0).animate({scrollTop:o},100)}},e}(_t()),Ct=function(t){function e(){return t.apply(this,arguments)||this}return x(e,t),e.initAttrs=function(e){var n;t.initAttrs.call(this,e);var r=e.discussion?a().translator.trans("flarum-tags.forum.choose_tags.edit_title",{title:m("em",null,e.discussion.title())}):a().translator.trans("flarum-tags.forum.choose_tags.title");e.className=L()(e.className,"TagDiscussionModal"),e.title=gt()(r),e.allowResetting=!!a().forum.attribute("canBypassTagCounts"),e.limits={allowBypassing:e.allowResetting,max:{primary:a().forum.attribute("maxPrimaryTags"),secondary:a().forum.attribute("maxSecondaryTags")},min:{primary:a().forum.attribute("minPrimaryTags"),secondary:a().forum.attribute("minSecondaryTags")}},e.requireParentTag=!0,e.selectableTags=function(){return vt(e.discussion)},null!=e.selectedTags||(e.selectedTags=(null==(n=e.discussion)?void 0:n.tags())||[]),e.canSelect=function(t){return t.canStartDiscussion()};var o=e.onsubmit||null;e.onsubmit=function(t){var n=e.discussion;n&&n.save({relationships:{tags:t}}).then((function(){a().current.matches(pt())&&a().current.get("stream").update(),m.redraw()})),o&&o(t)}},e}(St);function kt(){(0,f.extend)(ut(),"moderationControls",(function(t,e){e.canTag()&&t.add("tags",m(dt(),{icon:"fas fa-tag",onclick:function(){return app.modal.show(Ct,{discussion:e})}},app.translator.trans("flarum-tags.forum.discussion_controls.edit_tags_button")))}))}const At=flarum.core.compat["forum/components/DiscussionComposer"];var Dt=n.n(At);function Et(){(0,f.extend)(h().prototype,"newDiscussionAction",(function(t){var e=this.currentTag();if(e){var n=e.parent(),r=n?[n,e]:[e];t.then((function(t){return t.fields.tags=r}))}else app.composer.fields.tags=[]})),(0,f.extend)(Dt().prototype,"oninit",(function(){app.tagList.load(["parent"]).then((function(){return m.redraw()}))})),Dt().prototype.chooseTags=function(){var t=this;vt().length&&app.modal.show(Ct,{selectedTags:(this.composer.fields.tags||[]).slice(0),onsubmit:function(e){t.composer.fields.tags=e,t.$("textarea").focus()}})},(0,f.extend)(Dt().prototype,"headerItems",(function(t){var e=this.composer.fields.tags||[],n=vt();t.add("tags",m("a",{className:L()(["DiscussionComposer-changeTags",!n.length&&"disabled"]),onclick:this.chooseTags.bind(this)},e.length?it(e):m("span",{className:"TagLabel untagged"},app.translator.trans("flarum-tags.forum.composer_discussion.choose_tags_link"))),10)})),(0,f.override)(Dt().prototype,"onsubmit",(function(t){var e=this,n=this.composer.fields.tags||[],r=n.filter((function(t){return null!==t.position()&&!t.isChild()})),a=n.filter((function(t){return null===t.position()})),o=vt(),s=parseInt(app.forum.attribute("minPrimaryTags")),i=parseInt(app.forum.attribute("minSecondaryTags")),c=parseInt(app.forum.attribute("maxPrimaryTags")),l=parseInt(app.forum.attribute("maxSecondaryTags"));(!n.length&&0!==c&&0!==l||r.length<s||a.length<i)&&o.length?app.modal.show(Ct,{selectedTags:n,onsubmit:function(n){e.composer.fields.tags=n,t()}}):t()})),(0,f.extend)(Dt().prototype,"data",(function(t){t.relationships=t.relationships||{},t.relationships.tags=this.composer.fields.tags}))}const Ot=flarum.core.compat["common/extenders"];var Mt=n.n(Ot);const Rt=flarum.core.compat["common/models/Discussion"];var jt=n.n(Rt);const qt=flarum.core.compat["forum/components/EventPost"];var Bt=function(t){function e(){return t.apply(this,arguments)||this}x(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e);var n=e.post.content()[0],r=e.post.content()[1];function a(t,e){return t.filter((function(t){return-1===e.indexOf(t)})).map((function(t){return app.store.getById("tags",t)}))}e.tagsAdded=a(r,n),e.tagsRemoved=a(n,r)};var n=e.prototype;return n.icon=function(){return"fas fa-tag"},n.descriptionKey=function(){return this.attrs.tagsAdded.length?this.attrs.tagsRemoved.length?"flarum-tags.forum.post_stream.added_and_removed_tags_text":"flarum-tags.forum.post_stream.added_tags_text":"flarum-tags.forum.post_stream.removed_tags_text"},n.descriptionData=function(){var t={};return this.attrs.tagsAdded.length&&(t.tagsAdded=app.translator.trans("flarum-tags.forum.post_stream.tags_text",{tags:it(this.attrs.tagsAdded,{link:!0}),count:this.attrs.tagsAdded.length})),this.attrs.tagsRemoved.length&&(t.tagsRemoved=app.translator.trans("flarum-tags.forum.post_stream.tags_text",{tags:it(this.attrs.tagsRemoved,{link:!0}),count:this.attrs.tagsRemoved.length})),t},e}(n.n(qt)());const Ht=flarum.core.compat["common/utils/computed"];var Ft=n.n(Ht);const Gt=flarum.core.compat["common/Model"];var Vt=n.n(Gt),Kt=function(t){function e(){return t.apply(this,arguments)||this}x(e,t);var n=e.prototype;return n.name=function(){return Vt().attribute("name").call(this)},n.slug=function(){return Vt().attribute("slug").call(this)},n.description=function(){return Vt().attribute("description").call(this)},n.color=function(){return Vt().attribute("color").call(this)},n.backgroundUrl=function(){return Vt().attribute("backgroundUrl").call(this)},n.backgroundMode=function(){return Vt().attribute("backgroundMode").call(this)},n.icon=function(){return Vt().attribute("icon").call(this)},n.position=function(){return Vt().attribute("position").call(this)},n.parent=function(){return Vt().hasOne("parent").call(this)},n.children=function(){return Vt().hasMany("children").call(this)},n.defaultSort=function(){return Vt().attribute("defaultSort").call(this)},n.isChild=function(){return Vt().attribute("isChild").call(this)},n.isHidden=function(){return Vt().attribute("isHidden").call(this)},n.discussionCount=function(){return Vt().attribute("discussionCount").call(this)},n.lastPostedAt=function(){return Vt().attribute("lastPostedAt",Vt().transformDate).call(this)},n.lastPostedDiscussion=function(){return Vt().hasOne("lastPostedDiscussion").call(this)},n.isRestricted=function(){return Vt().attribute("isRestricted").call(this)},n.canStartDiscussion=function(){return Vt().attribute("canStartDiscussion").call(this)},n.canAddToDiscussion=function(){return Vt().attribute("canAddToDiscussion").call(this)},n.isPrimary=function(){return Ft()("position","parent",(function(t,e){return null!==t&&!1===e})).call(this)},e}(Vt());const $t=[(new(Mt().Store)).add("tags",Kt)],Ut=[].concat($t,[(new(Mt().Routes)).add("tags","/tags",K).add("tag","/t/:tags",h()).helper("tag",(function(t){return a().route("tag",{tags:t.slug()})})),(new(Mt().PostTypes)).add("discussionTagged",Bt),new(Mt().Model)(jt()).hasMany("tags").attribute("canTag")]),Xt={"tags/utils/sortTags":V,"tags/models/Tag":Kt,"tags/helpers/tagsLabel":it,"tags/helpers/tagIcon":I,"tags/helpers/tagLabel":G,"tags/components/TagSelectionModal":St,"tags/states/TagListState":d},Yt=Object.assign(Xt,{"tags/addTagFilter":tt,"tags/addTagControl":kt,"tags/components/TagHero":W,"tags/components/TagDiscussionModal":Ct,"tags/components/TagsPage":K,"tags/components/ToggleButton":Pt,"tags/components/DiscussionTaggedPost":Bt,"tags/components/TagLinkButton":P,"tags/addTagList":$,"tags/addTagLabels":ct,"tags/addTagComposer":Et,"tags/utils/getSelectableTags":vt}),zt=flarum.core;a().initializers.add("flarum-tags",(function(){a().tagList=new d,$(),tt(),ct(),kt(),Et()})),Object.assign(zt.compat,Yt)}},e={};function n(r){var a=e[r];if(void 0!==a)return a.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{extend:()=>a.X});var t=n(361),e={};for(const n in t)"default"!==n&&(e[n]=()=>t[n]);n.d(r,e);var a=n(627)})(),module.exports=r})();
//# sourceMappingURL=forum.js.map