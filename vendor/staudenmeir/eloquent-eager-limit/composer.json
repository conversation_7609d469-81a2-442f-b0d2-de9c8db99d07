{"name": "staudenmeir/eloquent-eager-limit", "description": "Laravel Eloquent eager loading with limit", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "illuminate/database": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"Staudenmeir\\EloquentEagerLimit\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true}