Symfony Polyfill / Intl: MessageFormatter
=========================================

This component provides a fallback implementation for the
[`MessageFormatter`](https://php.net/MessageFormatter) class provided
by the [Intl](https://php.net/intl) extension.

More information can be found in the
[main Polyfill README](https://github.com/symfony/polyfill/blob/main/README.md).

License
=======

This library is released under the [MIT license](LICENSE).
