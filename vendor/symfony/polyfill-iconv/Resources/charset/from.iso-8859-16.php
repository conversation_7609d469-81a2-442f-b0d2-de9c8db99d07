<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => ' ',
  '�' => 'Ą',
  '�' => 'ą',
  '�' => 'Ł',
  '�' => '€',
  '�' => '„',
  '�' => 'Š',
  '�' => '§',
  '�' => 'š',
  '�' => '©',
  '�' => 'Ș',
  '�' => '«',
  '�' => 'Ź',
  '�' => '­',
  '�' => 'ź',
  '�' => 'Ż',
  '�' => '°',
  '�' => '±',
  '�' => 'Č',
  '�' => 'ł',
  '�' => 'Ž',
  '�' => '”',
  '�' => '¶',
  '�' => '·',
  '�' => 'ž',
  '�' => 'č',
  '�' => 'ș',
  '�' => '»',
  '�' => 'Œ',
  '�' => 'œ',
  '�' => 'Ÿ',
  '�' => 'ż',
  '�' => 'À',
  '�' => 'Á',
  '�' => 'Â',
  '�' => 'Ă',
  '�' => 'Ä',
  '�' => 'Ć',
  '�' => 'Æ',
  '�' => 'Ç',
  '�' => 'È',
  '�' => 'É',
  '�' => 'Ê',
  '�' => 'Ë',
  '�' => 'Ì',
  '�' => 'Í',
  '�' => 'Î',
  '�' => 'Ï',
  '�' => 'Đ',
  '�' => 'Ń',
  '�' => 'Ò',
  '�' => 'Ó',
  '�' => 'Ô',
  '�' => 'Ő',
  '�' => 'Ö',
  '�' => 'Ś',
  '�' => 'Ű',
  '�' => 'Ù',
  '�' => 'Ú',
  '�' => 'Û',
  '�' => 'Ü',
  '�' => 'Ę',
  '�' => 'Ț',
  '�' => 'ß',
  '�' => 'à',
  '�' => 'á',
  '�' => 'â',
  '�' => 'ă',
  '�' => 'ä',
  '�' => 'ć',
  '�' => 'æ',
  '�' => 'ç',
  '�' => 'è',
  '�' => 'é',
  '�' => 'ê',
  '�' => 'ë',
  '�' => 'ì',
  '�' => 'í',
  '�' => 'î',
  '�' => 'ï',
  '�' => 'đ',
  '�' => 'ń',
  '�' => 'ò',
  '�' => 'ó',
  '�' => 'ô',
  '�' => 'ő',
  '�' => 'ö',
  '�' => 'ś',
  '�' => 'ű',
  '�' => 'ù',
  '�' => 'ú',
  '�' => 'û',
  '�' => 'ü',
  '�' => 'ę',
  '�' => 'ț',
  '�' => 'ÿ',
);

$result =& $data;
unset($data);

return $result;
