name: "testing"

on:
    push:
        branches: [ master ]
    pull_request:
        branches: [ master ]

jobs:
    qa:
        name: Quality assurance
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Validate composer.json and composer.lock
              run: composer validate

            - name: Cache Composer packages
              id: composer-cache
              uses: actions/cache@v4
              with:
                  path: vendor
                  key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-php-

            - name: Install dependencies
              if: steps.composer-cache.outputs.cache-hit != 'true'
              run: composer install --prefer-dist --no-progress

            - name: Coding Standard
              run: composer run-script cs

    tests:
        name: Tests
        runs-on: ubuntu-latest

        strategy:
            matrix:
                php:
                    - 7.2
                    - 7.3
                    - 7.4
                    - 8.0
                    - 8.1
                    - 8.2
                    - 8.3
                    - 8.4

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Install PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}

            - name: Cache PHP dependencies
              uses: actions/cache@v4
              with:
                  path: vendor
                  key: ${{ runner.os }}-php-${{ matrix.php }}-composer-${{ hashFiles('**/composer.json') }}
                  restore-keys: ${{ runner.os }}-php-${{ matrix.php }}-composer-

            - name: Install dependencies
              run: composer install --prefer-dist --no-progress

            - name: Tests
              run: composer test
