{"name": "middlewares/request-handler", "type": "library", "description": "Middleware to execute request handlers", "license": "MIT", "keywords": ["psr-7", "psr-15", "middleware", "server", "http", "request", "handler", "controller", "invoke"], "homepage": "https://github.com/middlewares/request-handler", "support": {"issues": "https://github.com/middlewares/request-handler/issues"}, "require": {"php": "^7.2 || ^8.0", "middlewares/utils": "^2 || ^3 || ^4", "psr/http-server-middleware": "^1", "psr/container": "^1.0||^2.0"}, "require-dev": {"phpunit/phpunit": "^8 || ^9", "friendsofphp/php-cs-fixer": "^3", "oscarotero/php-cs-fixer-config": "^2", "squizlabs/php_codesniffer": "^3", "phpstan/phpstan": "^1 || ^2", "laminas/laminas-diactoros": "^2 || ^3"}, "autoload": {"psr-4": {"Middlewares\\": "src/"}}, "autoload-dev": {"psr-4": {"Middlewares\\Tests\\": "tests/"}}, "scripts": {"cs": "phpcs", "cs-fix": "php-cs-fixer fix", "phpstan": "phpstan analyse", "test": "phpunit", "coverage": "phpunit --coverage-text", "coverage-html": "phpunit --coverage-html=coverage"}}