# Doctrine DBAL

| [4.0-dev][4.0] | [3.3][3.3] | [2.13][2.13] |
|:----------------:|:----------:|:----------:|
| [![GitHub Actions][GA 4.0 image]][GA 4.0] | [![GitHub Actions][GA 3.3 image]][GA 3.3] | [![GitHub Actions][GA 2.13 image]][GA 2.13] |
| [![AppVeyor][AppVeyor 4.0 image]][AppVeyor 4.0] | [![AppVeyor][AppVeyor 3.3 image]][AppVeyor 3.3] | [![AppVeyor][AppVeyor 2.13 image]][AppVeyor 2.13] |
| [![Code Coverage][Coverage image]][CodeCov 4.0] | [![Code Coverage][Coverage 3.3 image]][CodeCov 3.3] | [![Code Coverage][Coverage 2.13 image]][CodeCov 2.13] |
| N/A | [![Code Coverage][TypeCov 3.3 image]][TypeCov 3.3] | N/A |

Powerful database abstraction layer with many features for database schema introspection, schema management and PDO abstraction.

## More resources:

* [Website](http://www.doctrine-project.org/projects/dbal.html)
* [Documentation](http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/)
* [Issue Tracker](https://github.com/doctrine/dbal/issues)

  [Coverage image]: https://codecov.io/gh/doctrine/dbal/branch/4.0.x/graph/badge.svg
  [4.0]: https://github.com/doctrine/dbal/tree/4.0.x
  [CodeCov 4.0]: https://codecov.io/gh/doctrine/dbal/branch/4.0.x
  [AppVeyor 4.0]: https://ci.appveyor.com/project/doctrine/dbal/branch/4.0.x
  [AppVeyor 4.0 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/4.0.x?svg=true
  [GA 4.0]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A4.0.x
  [GA 4.0 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg

  [Coverage 3.3 image]: https://codecov.io/gh/doctrine/dbal/branch/3.3.x/graph/badge.svg
  [3.3]: https://github.com/doctrine/dbal/tree/3.3.x
  [CodeCov 3.3]: https://codecov.io/gh/doctrine/dbal/branch/3.3.x
  [AppVeyor 3.3]: https://ci.appveyor.com/project/doctrine/dbal/branch/3.3.x
  [AppVeyor 3.3 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/3.3.x?svg=true
  [GA 3.3]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.3.x
  [GA 3.3 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=3.3.x
  [TypeCov 3.3]: https://shepherd.dev/github/doctrine/dbal
  [TypeCov 3.3 image]: https://shepherd.dev/github/doctrine/dbal/coverage.svg

  [Coverage 2.13 image]: https://codecov.io/gh/doctrine/dbal/branch/2.13.x/graph/badge.svg
  [2.13]: https://github.com/doctrine/dbal/tree/2.13.x
  [CodeCov 2.13]: https://codecov.io/gh/doctrine/dbal/branch/2.13.x
  [AppVeyor 2.13]: https://ci.appveyor.com/project/doctrine/dbal/branch/2.13.x
  [AppVeyor 2.13 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/2.13.x?svg=true
  [GA 2.13]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A2.13.x
  [GA 2.13 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=2.13.x
