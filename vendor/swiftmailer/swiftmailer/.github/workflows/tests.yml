name: tests

on:
  push:
  pull_request:

jobs:
  linux_tests:
    runs-on: ubuntu-20.04

    services:
      mailcatcher:
        image: dockage/mailcatcher:0.7.1
        ports:
          - 4456:1025

    strategy:
      fail-fast: true
      matrix:
        php: ['7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1']

    name: PHP ${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, intl
          tools: composer:v2
          coverage: none

      - name: Prepare test config files
        run: |
          cp tests/acceptance.conf.php.default tests/acceptance.conf.php
          cp tests/smoke.conf.php.default tests/smoke.conf.php

      - name: Require Symfony PHPUnit Bridge 5.4 for PHP 8.1
        if: ${{ matrix.php >= 8.1 }}
        run: composer require symfony/phpunit-bridge:^5.4 --dev --prefer-dist --no-interaction --no-progress

      - name: Install dependencies
        uses: nick-invision/retry@v1
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --prefer-stable --prefer-dist --no-interaction --no-progress

      - name: Execute tests
        run: vendor/bin/simple-phpunit --verbose
        env:
          SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT: 1
