{"name": "illuminate/validation", "description": "The Illuminate Validation package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "ext-json": "*", "egulias/email-validator": "^2.1.10", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "illuminate/translation": "^8.0", "symfony/http-foundation": "^5.4", "symfony/mime": "^5.4"}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "suggest": {"ext-bcmath": "Required to use the multiple_of validation rule.", "illuminate/database": "Required to use the database presence verifier (^8.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}