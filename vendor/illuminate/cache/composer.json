{"name": "illuminate/cache", "description": "The Illuminate Cache package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0"}, "provide": {"psr/simple-cache-implementation": "1.0"}, "autoload": {"psr-4": {"Illuminate\\Cache\\": ""}}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "suggest": {"ext-memcached": "Required to use the memcache cache driver.", "illuminate/database": "Required to use the database cache driver (^8.0).", "illuminate/filesystem": "Required to use the file cache driver (^8.0).", "illuminate/redis": "Required to use the redis cache driver (^8.0).", "symfony/cache": "Required to PSR-6 cache bridge (^5.4)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}