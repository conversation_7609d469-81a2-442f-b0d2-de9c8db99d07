<?php

/**
* @package   s9e\TextFormatter
* @copyright Copyright (c) 2010-2023 The s9e authors
* @license   http://www.opensource.org/licenses/mit-license.php The MIT License
*/
namespace s9e\TextFormatter\Bundles;

abstract class Fatdown extends \s9e\TextFormatter\Bundle
{
	/**
	* @var s9e\TextFormatter\Parser Singleton instance used by parse()
	*/
	protected static $parser;

	/**
	* @var s9e\TextFormatter\Renderer Singleton instance used by render()
	*/
	protected static $renderer;

	/**
	* {@inheritdoc}
	*/
	public static function getJS()
	{
		return '(function(){const ca=[""],da=[0,0],ea=["","t"],x=["","id"],fa={flags:0},ha=["","type"],la={flags:514},ma={flags:3089},oa={flags:3201},pa=[65519,65457],sa=[32960,33025],ta=[65477,65409],ua=[39819,65457],va=[32896,33153],wa=[65519,65441],xa=[65424,65408],ya=[65408,65416],za=[63463,65441],Aa=[65408,65408],Ca=["","album_id"],Da=["","track_id"],Ea=["","type","id"],Fa=["","playlist_id"],Ga=["","user","type","id"],Ha=["","channel","clip_id"],A={c:[],q:!1},Ia={c:[],q:!0},Ja={"class":A},B={C:1,EM:1,EMAIL:1,STRONG:1,URL:1,
"html:b":1,"html:code":1,"html:i":1,"html:strong":1,"html:u":1},Ka={C:1,EM:1,EMAIL:1,STRONG:1,URL:1,"html:b":1,"html:code":1,"html:i":1,"html:rp":1,"html:rt":1,"html:strong":1,"html:u":1},La=[[/(?:open|play)\\.spotify\\.com\\/(?:intl-\\w+\\/|user\\/[-.\\w]+\\/)*((?:album|artist|episode|playlist|show|track)(?:[:\\/][-.\\w]+)+)/,x]];
function Ma(a,b){let c={},d;for(d in b.b){let v=b.b[d];var l=!1;if(d in a.b){l=v.c;var k=d,g=a.b[d];C.z=k;for(let p=0;p<l.length&&(g=l[p](g,k),!1!==g);++p);delete C.z;l=g}!1!==l?c[d]=l:v.q&&F(a)}Na(a,c)}
const G=[Ma],Pa=[function(a){return Oa(a,/^[-0-9A-Za-z_]+$/)}],Qa={c:[function(a){let b=/^(?=\\d)(?:(\\d+)h)?(?:(\\d+)m)?(?:(\\d+)s)?$/.exec(a);return b?3600*(b[1]||0)+60*(b[2]||0)+(+b[3]||0):/^(?:0|[1-9]\\d*)$/.test(a)?a:!1}],q:!1},Xa=[function(a){{var b=Ra.urlConfig,c=C;let d=Ua(a.replace(/^\\s+/,"").replace(/\\s+$/,""));(b=Va(b,d))?(c&&(d.attrValue=a,c.add("err",b,d)),a=!1):a=Wa(d)}return a}],Ya={c:Pa,q:!0},Za={c:[function(a){return Oa(a,/^[- +,.0-9A-Za-z_]+$/)}],q:!1},$a={c:Xa,q:!0},ab={m:B,flags:268,
n:B},bb={m:B,flags:3460,n:B},cb={m:B,flags:3456,n:B},db={d:da,b:{},i:0,c:G,f:10,e:{flags:66},g:5E3},eb={d:ta,b:{},i:0,c:G,f:10,e:{flags:2},g:5E3},fb={d:ta,b:{},i:0,c:G,f:10,e:fa,g:5E3},gb={d:pa,b:{},i:0,c:G,f:10,e:{flags:512},g:5E3},hb={m:{C:1,EM:1,EMAIL:1,LI:1,STRONG:1,URL:1,"html:b":1,"html:code":1,"html:i":1,"html:li":1,"html:strong":1,"html:u":1},flags:264,n:B},ib={m:{C:1,EM:1,EMAIL:1,STRONG:1,URL:1,"html:b":1,"html:code":1,"html:dd":1,"html:dt":1,"html:i":1,"html:strong":1,"html:u":1},flags:256,
n:B},jb={m:{C:1,EM:1,EMAIL:1,STRONG:1,TD:1,TH:1,URL:1,"html:b":1,"html:code":1,"html:i":1,"html:strong":1,"html:td":1,"html:th":1,"html:u":1},flags:256,n:B},nb={d:sa,b:{id:A},i:2,c:G,f:10,e:ma,g:5E3},ob={d:va,b:{"char":Ia},i:8,c:G,f:10,e:ma,g:5E3},pb={align:{c:[function(a){return a.toLowerCase()},function(a){return Oa(a,/^(?:center|justify|left|right)$/)}],q:!1}},qb={d:ta,b:{},i:3,c:G,f:10,e:{m:B,flags:260,n:B},g:5E3},rb={d:[65408,65418],b:{},i:1,c:G,f:10,e:bb,g:5E3},sb={d:ya,b:{},i:9,c:G,f:10,e:cb,
g:5E3},tb={d:xa,b:{},i:1,c:G,f:10,e:bb,g:5E3},ub={d:Aa,b:{},i:14,c:G,f:10,e:cb,g:5E3},vb={d:[65408,65412],b:{},i:11,c:G,f:10,e:{m:{C:1,EM:1,EMAIL:1,STRONG:1,TD:1,TH:1,TR:1,URL:1,"html:b":1,"html:code":1,"html:i":1,"html:strong":1,"html:td":1,"html:th":1,"html:tr":1,"html:u":1},flags:3456,n:B},g:5E3},wb={d:ya,b:{},i:9,c:G,f:10,e:{m:{C:1,EM:1,EMAIL:1,STRONG:1,TBODY:1,TD:1,TH:1,THEAD:1,TR:1,URL:1,"html:b":1,"html:code":1,"html:i":1,"html:strong":1,"html:tbody":1,"html:td":1,"html:th":1,"html:thead":1,
"html:tr":1,"html:u":1},flags:3456,n:B},g:5E3},xb=\'<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:html="urn:s9e:TextFormatter:html" exclude-result-prefixes="html"><xsl:output method="html" encoding="utf-8" indent="no"/><xsl:decimal-format decimal-separator="."/><xsl:param$pMEDIAEMBED_THEME"/><xsl:param$pTASKLISTS_EDITABLE"/>$aBANDCAMP"><$w$hbandcamp"$l$g400px"><$w$l$e100%"><$v$k"$r$x$qno"$l$f><$i$psrc">//bandcamp.com/EmbeddedPlayer/size=large/minimal=true/<$s><$t$m@album_id">album=$b@album_id"/><$u$m@track_num">/t=$b@track_num"/></$u></$t><$o>track=$b@track_id"/></$o></$s><$u$m$MEDIAEMBED_THEME=\\\'dark\\\'">/bgcol=333333/linkcol=0f91ff</$u></$i></$v></$w></$w>$c$aC"><code>$d</code>$c$aCODE"><pre><code><$u$m@lang"><$i$pclass">language-$b@lang"/></$i></$u>$d</code></pre>$c$aDAILYMOTION"><$w$hdailymotion"$l$g640px"><$w$l$e56.25%"><$v$k"$r$x$qno"$l$f><$i$psrc">//www.dailymotion.com/embed/video/$b@id"/><$u$m@t">?start=$b@t"/></$u></$i></$v></$w></$w>$c$aDEL|EM|H1|H2|H3|H4|H5|H6|STRONG|SUB|SUP|TABLE|TBODY|THEAD|TR|html:b|html:br|html:code|html:dd|html:del|html:dl|html:dt|html:i|html:ins|html:li|html:ol|html:pre|html:rb|html:rp|html:rt|html:rtc|html:ruby|html:strong|html:sub|html:sup|html:table|html:tbody|html:tfoot|html:thead|html:tr|html:u|html:ul|p"><xsl:element$p{translate(local-name(),\\\'ABDEGHLMNOPRSTUY\\\',\\\'abdeghlmnoprstuy\\\')}">$d</xsl:element>$c$aEMAIL"><a href="mailto:{@email}">$d</a>$c$aESC">$d$c$aFACEBOOK"><$v$hfacebook"$k"$n$y$r$x onload="let c=new MessageChannel;c.port1.onmessage=e=&gt;this.style.height=e.data+\\\'px\\\';this.contentWindow.postMessage(\\\'s9e:init\\\',\\\'*\\\',[c.port2])"$qno"$lborder:0;height:360px;max-width:640px;width:100%"><$i$psrc">https://s9e.github.io/$v/2/facebook.min.html#<$s><$t$m@user">$b@user"/>/<$s><$t$m@type=\\\'r\\\'or@type=\\\'v\\\'">video</$t><$o>post</$o></$s>s/<$s><$t$m@id">$b@id"/></$t><$o>pfbid$b@pfbid"/></$o></$s></$t><$t$m@id">$b@type"/>$b@id"/></$t><$o>pfbid$b@pfbid"/></$o></$s></$i></$v>$c$aFP|HE">$b@char"/>$c$aHC"><xsl:comment>$b@content"/></xsl:comment>$c$aHR"><hr/>$c$aIMG"><img src="{@src}">$jalt|@title"/></img>$c$aISPOILER"><$w class="spoiler"$n$y onclick="this.removeAttribute(\\\'style\\\')"$lbackground:#444;color:transparent">$d</$w>$c$aLI"><li><$u$mTASK"><$i$pdata-s9e-livepreview-ignore-attrs">data-task-id</$i><$i$pdata-task-id">$bTASK/@id"/></$i><$i$pdata-task-state">$bTASK/@state"/></$i></$u>$d</li>$c$aLIST"><$s><$t$mnot(@type)"><ul>$d</ul></$t><$o><ol>$jstart"/>$d</ol></$o></$s>$c$aLIVELEAK"><$w$hliveleak"$l$g640px"><$w$l$e56.25%"><$v$k"$r$x$qno" src="//www.liveleak.com/e/{@id}"$l$f/></$w></$w>$c$aQUOTE"><blockquote>$d</blockquote>$c$aSOUNDCLOUD"><$v$hsoundcloud"$k"$r$x$qno"><$i$psrc">https://w.soundcloud.com/player/?url=<$s><$t$m$z">https%3A//api.soundcloud.com/playlists/$b$z"/>%3Fsecret_token%3D$b@secret_token"/></$t><$t$m@track_id">https%3A//api.soundcloud.com/tracks/$b@track_id"/>%3Fsecret_token%3D$b@secret_token"/></$t><$o><$u$mnot(contains(@id,\\\'://\\\'))">https%3A//soundcloud.com/</$u>$b@id"/></$o></$s></$i><$i$p$y>border:0;height:<$s><$t$m$z or contains(@id,\\\'/sets/\\\')">450</$t><$o>166</$o></$s>px;max-width:900px;width:100%</$i></$v>$c$aSPOILER"><details class="spoiler"$nopen">$d</details>$c$aSPOTIFY"><$v$hspotify" allow="encrypted-media"$k"$r$x$qno" src="https://open.spotify.com/embed/{translate(@id,\\\':\\\',\\\'/\\\')}{@path}"><$i$p$y>border:0;border-radius:12px;height:<$s><$t$mstarts-with(@id,\\\'episode\\\')or starts-with(@id,\\\'show\\\')or starts-with(@id,\\\'track\\\')">152</$t><$o>380</$o></$s>px;max-width:900px;width:100%</$i></$v>$c$aTASK"><input data-task-id="{@id}"$ndata-task-id" type="checkbox"><$u$m@state=\\\'checked\\\'"><$i$pchecked"/></$u><$u$mnot($TASKLISTS_EDITABLE)"><$i$pdisabled"/></$u></input>$c$aTD"><td><$u$m@align"><$i$p$y>text-align:$b@align"/></$i></$u>$d</td>$c$aTH"><th><$u$m@align"><$i$p$y>text-align:$b@align"/></$i></$u>$d</th>$c$aTWITCH"><$w$htwitch"$l$g640px"><$w$l$e56.25%"><$v$k"$r$x onload="this.contentWindow.postMessage(\\\'\\\',\\\'*\\\')"$qno" src="https://s9e.github.io/$v/2/twitch.min.html#channel={@channel};clip_id={@clip_id};t={@t};video_id={@video_id}"$l$f/></$w></$w>$c$aURL"><a href="{@url}">$jtitle"/>$d</a>$c$aVIMEO"><$w$hvimeo"$l$g640px"><$w$l$e56.25%"><$v$k"$r$x$qno"$l$f><$i$psrc">//player.vimeo.com/video/$b@id"/><$u$m@h">?h=$b@h"/></$u><$u$m@t">#t=$b@t"/></$u></$i></$v></$w></$w>$c$aVINE"><$w$hvine"$l$g480px"><$w$l$e100%"><$v$k"$r$x$qno" src="https://vine.co/v/{@id}/embed/simple?audio=1"$l$f/></$w></$w>$c$aYOUTUBE"><$w$hyoutube"$l$g640px"><$w$l$e56.25%"><$v$k"$r$x$qno"$lbackground:url(https://i.ytimg.com/vi/{@id}/hqdefault.jpg) 50% 50% / cover;$f><$i$psrc">https://www.youtube.com/embed/$b@id"/><$s><$t$m@clip">?clip=$b@clip"/>&amp;clipt=$b@clipt"/></$t><$o><$u$m@list">?list=$b@list"/></$u><$u$m@t"><$s><$t$m@list">&amp;</$t><$o>?</$o></$s>start=$b@t"/></$u></$o></$s></$i></$v></$w></$w>$c$abr"><br/>$c$ae|i|s"/>$ahtml:abbr"><abbr>$jtitle"/>$d</abbr>$c$ahtml:div"><div>$jclass"/>$d</div>$c$ahtml:img"><img>$jalt|@height|@src|@title|@width"/>$d</img>$c$ahtml:$w"><$w>$jclass"/>$d</$w>$c$ahtml:td"><td>$jcol$w|@row$w"/>$d</td>$c$ahtml:th"><th>$jcol$w|@row$w|@scope"/>$d</th>$c</xsl:stylesheet>\'.replace(/\\$[a-z]/g,
function(a){return{$a:\'<xsl:template match="\',$b:\'<xsl:value-of select="\',$c:"</xsl:template>",$d:"<xsl:apply-templates/>",$e:"display:block;overflow:hidden;position:relative;padding-bottom:",$f:\'border:0;height:100%;left:0;position:absolute;width:100%"\',$g:"display:inline-block;width:100%;max-width:",$h:\' data-s9e-mediaembed="\',$i:"xsl:attribute",$j:\'<xsl:copy-of select="@\',$k:\' allowfullscreen="\',$l:\' style="\',$m:\' test="\',$n:\' data-s9e-livepreview-ignore-attrs="\',$o:"xsl:otherwise",$p:\' name="\',
$q:\' scrolling="\',$r:\' loading="\',$s:"xsl:choose",$t:"xsl:when",$u:"xsl:if",$v:"iframe",$w:"span",$x:\'lazy"\',$y:\'style"\',$z:"@playlist_id"}[a]});function Oa(a,b){return b.test(a)?a:!1}
function Ua(a){let b=/^(?:([a-z][-+.\\w]*):)?(?:\\/\\/(?:([^:\\/?#]*)(?::([^\\/?#]*)?)?@)?(?:(\\[[a-f\\d:]+\\]|[^:\\/?#]+)(?::(\\d*))?)?(?![^\\/?#]))?([^?#]*)(\\?[^#]*)?(#.*)?$/i.exec(a),c={};"scheme user pass host port path query fragment".split(" ").forEach(function(d,l){c[d]=""<b[l+1]?b[l+1]:""});c.scheme=c.scheme.toLowerCase();c.host=c.host.replace(/[\\u3002\\uff0e\\uff61]/g,".").replace(/\\.+$/g,"");/[^\\x00-\\x7F]/.test(c.host)&&"undefined"!==typeof punycode&&(c.host=punycode.toASCII(c.host));return c}
function Wa(a){let b="";""!==a.scheme&&(b+=a.scheme+":");""!==a.host?(b+="//",""!==a.user&&(b+=yb(decodeURIComponent(a.user)),""!==a.pass&&(b+=":"+yb(decodeURIComponent(a.pass))),b+="@"),b+=a.host,""!==a.port&&(b+=":"+a.port)):"file"===a.scheme&&(b+="//");let c=a.path+a.query+a.fragment;c=c.replace(/%.?[a-f]/g,function(d){return d.toUpperCase()},c);b+=zb(c);a.scheme||(b=b.replace(/^([^\\/]*):/,"$1%3A"));return b}
function zb(a){return a.replace(/[^\\u0020-\\u007E]+/g,encodeURIComponent).replace(/%(?![0-9A-Fa-f]{2})|[^!#-&*-;=?-Z_a-z~]/g,function(b){return"%"+b[0].charCodeAt(0).toString(16).toUpperCase()})}
function Va(a,b){if(""!==b.scheme&&!a.S.test(b.scheme))return"URL scheme is not allowed";if(""!==b.host){var c;if(c=!/^(?!-)[-a-z0-9]{0,62}[a-z0-9](?:\\.(?!-)[-a-z0-9]{0,62}[a-z0-9])*$/i.test(b.host)){a:if(c=b.host,/^\\d+\\.\\d+\\.\\d+\\.\\d+$/.test(c))for(var d=4,l=c.split(".");0<=--d;){if("0"===l[d][0]||255<l[d]){c=!1;break a}}else c=!1;if(c=!c)c=b.host.replace(/^\\[(.*)\\]$/,"$1",b.host),c=!(/^([\\da-f]{0,4}:){2,7}(?:[\\da-f]{0,4}|\\d+\\.\\d+\\.\\d+\\.\\d+)$/.test(c)&&c)}if(c)return"URL host is invalid";if(a.U&&
a.U.test(b.host)||a.X&&!a.X.test(b.host))return"URL host is not allowed"}else if(/^(?:(?:f|ht)tps?)$/.test(b.scheme))return"Missing host"}function Ab(a){let b=document.createElement("b");Ab=function(c){b.innerHTML=c.replace(/</g,"&lt;");return b.textContent};return Ab(a)}function Bb(a){return a.replace(/[<>&"]/g,b=>({"<":"&lt;",">":"&gt;","&":"&amp;",\'"\':"&quot;"})[b])}function Cb(a){return a.replace(/[<>&]/g,b=>({"<":"&lt;",">":"&gt;","&":"&amp;"})[b])}
function yb(a){return encodeURIComponent(a).replace(/[!\'()*]/g,b=>"%"+b.charCodeAt(0).toString(16).toUpperCase())}function Db(){this.p={};this.r=[]}Db.prototype.add=function(a,b,c){c=c||{};"attrName"in c||!this.z||(c.attrName=this.z);"tag"in c||!this.l||(c.tag=this.l);this.p[a]&&this.p[a].forEach(d=>{d(b,c)});this.r.push([a,b,c])};Db.prototype.getLogs=function(){return this.r};Db.prototype.on=function(a,b){this.p[a].push(b)};function Eb(a,b){C.add("debug",a,b)}
function Fb(a,b,c,d,l){this.l=+a;this.name=b;this.j=+c;this.k=+d;this.r=+l||0;this.b={};this.O=[];isNaN(a+c+d)&&F(this)}Fb.prototype.p=!1;function Gb(a,b){a.O.push(b);a.p&&F(b)}function F(a){a.p||(a.p=!0,a.O.forEach(function(b){F(b)}))}function Hb(a,b){Ib(a,b)?(a.A=b,b.H=a,Gb(a,b)):Ib(b,a)&&(a.H=b,b.A=a)}function Ib(a,b){return a.name===b.name&&1===a.l&&2===b.l&&a.j<=a.j}function Jb(a){let b={};for(let c in a.b)b[c]=a.b[c];return b}
function Kb(a,b){return a.p||!Ib(b,a)||a.H&&a.H!==b||b.A&&b.A!==a?!1:!0}function Na(a,b){a.b={};for(let c in b)a.b[c]=b[c]}let Lb,Mb,H,Nb,I,Ob,C=new Db,Pb,J,L;
const Sb={Autoemail:{u:function(a,b){b.forEach(c=>{let d=M(1,"EMAIL",c[0][1],0,0);d.b.email=c[0][0];c=Qb("EMAIL",c[0][1]+c[0][0].length,0);Hb(d,c)})},w:"@",x:/\\b[-a-z0-9_+.]+@[-a-z0-9.]*[a-z0-9]/ig,y:5E4},Autolink:{u:function(a,b){b.forEach(c=>{var d=c[0][1],l=c[0][0].replace(/(?:(?![-=+)\\/_])[\\s!-.:-@[-`{-~])+$/,"");let k=d+l.length,g=Qb("URL",k,0);"."===l[3]&&(l="http://"+l);c=M(1,"URL",d,0,1);c.b.url=l;Hb(c,g);d=M(3,"v",d,k-d,1E3);Gb(c,d)})},w:":",x:/\\b(?:ftp|https?|mailto):(?:[^\\s()\\[\\]\\uFF01-\\uFF0F\\uFF1A-\\uFF20\\uFF3B-\\uFF40\\uFF5B-\\uFF65]|\\([^\\s()]*\\)|\\[\\w*\\])+/ig,
y:5E4},Escaper:{u:function(a,b){b.forEach(c=>{O("ESC",c[0][1],1,c[0][1]+c[0][0].length,0)})},w:"\\\\",x:/\\\\[-!#()*+.:<>@[\\\\\\]^_`{|}~]/g,y:5E4},FancyPants:{u:function(a){function b(f,m,n,t){f=M(3,q,f,m,t||0);f.b[e]=n;return f}function c(){if(!(0>a.indexOf("...")&&0>a.indexOf("--")))for(var f={"--":"\\u2013","---":"\\u2014","...":"\\u2026"},m=/---?|\\.\\.\\./g,n;n=m.exec(a);)b(n.index,n[0].length,f[n[0]])}function d(){if(!(0>a.indexOf("/")))for(var f={"0/3":"\\u2189","1/10":"\\u2152","1/2":"\\u00bd","1/3":"\\u2153",
"1/4":"\\u00bc","1/5":"\\u2155","1/6":"\\u2159","1/7":"\\u2150","1/8":"\\u215b","1/9":"\\u2151","2/3":"\\u2154","2/5":"\\u2156","3/4":"\\u00be","3/5":"\\u2157","3/8":"\\u215c","4/5":"\\u2158","5/6":"\\u215a","5/8":"\\u215d","7/8":"\\u215e"},m,n=/\\b(?:0\\/3|1\\/(?:[2-9]|10)|2\\/[35]|3\\/[458]|4\\/5|5\\/[68]|7\\/8)\\b/g;m=n.exec(a);)b(m.index,m[0].length,f[m[0]])}function l(){if(!(0>a.indexOf("<<")))for(var f,m=/<<( ?)(?! )[^\\n<>]*?[^\\n <>]\\1>>(?!>)/g;f=m.exec(a);){let n=b(f.index,2,"\\u00ab");f=b(f.index+f[0].length-2,2,
"\\u00bb");Gb(n,f)}}function k(){if(!(0>a.indexOf("!=")&&0>a.indexOf("=/=")))for(var f,m=/\\b (?:!|=\\/)=(?= \\b)/g;f=m.exec(a);)b(f.index+1,f[0].length-1,"\\u2260")}function g(f,m,n,t){for(var y;y=m.exec(a);){let z=b(y.index+y[0].indexOf(f),1,n);y=b(y.index+y[0].length-1,1,t);Gb(z,y)}}function v(){if(h)for(var f,m=/[a-z]\'|(?:^|\\s)\'(?=[a-z]|[0-9]{2})/gi;f=m.exec(a);)b(f.index+f[0].indexOf("\'"),1,"\\u2019",10)}function p(){if(h||r||!(0>a.indexOf("x")))for(var f={"\'s":"\\u2019","\'":"\\u2032","\' ":"\\u2032",
"\'x":"\\u2032",\'"\':"\\u2033",\'" \':"\\u2033",\'"x\':"\\u2033"},m,n=/[0-9](?:\'s|["\']? ?x(?= ?[0-9])|["\'])/g;m=n.exec(a);){"x"===m[0][m[0].length-1]&&b(m.index+m[0].length-1,1,"\\u00d7");let t=m[0].substring(1,3);f[t]&&b(m.index+1,1,f[t])}}function u(){if(!(0>a.indexOf("(")))for(var f={"(c)":"\\u00a9","(r)":"\\u00ae","(tm)":"\\u2122"},m=/\\((?:c|r|tm)\\)/gi,n;n=m.exec(a);)b(n.index,n[0].length,f[n[0].toLowerCase()])}const w={z:"char",K:"FP"};let e=w.z,h=0<=a.indexOf("\'"),r=0<=a.indexOf(\'"\'),q=w.K;"undefined"===
typeof w.ca&&(v(),h&&g("\'",/(?:^|\\W)\'.+?\'(?!\\w)/g,"\\u2018","\\u2019"),r&&g(\'"\',/(?:^|\\W)".+?"(?!\\w)/g,"\\u201c","\\u201d"));"undefined"===typeof w.$&&l();"undefined"===typeof w.aa&&(k(),p(),d());"undefined"===typeof w.ba&&c();"undefined"===typeof w.da&&u()}},HTMLComments:{u:function(a,b){b.forEach(c=>{let d=Ab(c[0][0].substring(4,c[0][0].length-3));d=d.replace(/[<>]/g,"");d=d.replace(/-+$/,"");d=d.replace(/--/g,"");M(3,"HC",c[0][1],c[0][0].length,0).b.content=d})},w:"\\x3c!--",x:/\\x3c!--(?!\\[if)[\\s\\S]*?--\\x3e/ig,
y:5E4},HTMLElements:{u:function(a,b){var c={a:{"":"URL",href:"url"},em:{"":"EM"},hr:{"":"HR"},s:{"":"S"},strong:{"":"STRONG"},sup:{"":"SUP"}};b.forEach(d=>{var l="/"===a[d[0][1]+1],k=d[0][1],g=d[0][0].length;let v=d[2-l][0].toLowerCase();var p=c&&c[v]&&c[v][""]?c[v][""]:"html:"+v;if(l)Qb(p,k,g);else for(l=/(<\\S+|[\'"\\s])\\/>$/.test(d[0][0])?O(p,k,g,k+g,0):M(1,p,k,g,0),d=d[3][0],k=/([a-z][-a-z0-9]*)(?:\\s*=\\s*("[^"]*"|\'[^\']*\'|[^\\s"\'=<>`]+))?/gi;p=k.exec(d);)g=p[1].toLowerCase(),p="undefined"!==typeof p[2]?
p[2]:g,c&&c[v]&&c[v][g]&&(g=c[v][g]),/^["\']/.test(p)&&(p=p.substring(1,p.length-1)),p=Ab(p),l.b[g]=p})},w:"<",x:/<(?:\\/((?:a(?:bbr)?|br?|code|d(?:[dlt]|el|iv)|em|hr|i(?:mg|ns)?|li|ol|pre|r(?:[bp]|tc?|uby)|s(?:pan|trong|u[bp])?|t(?:[dr]|able|body|foot|h(?:ead)?)|ul?))|((?:a(?:bbr)?|br?|code|d(?:[dlt]|el|iv)|em|hr|i(?:mg|ns)?|li|ol|pre|r(?:[bp]|tc?|uby)|s(?:pan|trong|u[bp])?|t(?:[dr]|able|body|foot|h(?:ead)?)|ul?))((?:\\s+[a-z][-a-z0-9]*(?:\\s*=\\s*(?:"[^"]*"|\'[^\']*\'|[^\\s"\'=<>`]+))?)*)\\s*\\/?)\\s*>/ig,y:5E4},
HTMLEntities:{u:function(a,b){b.forEach(c=>{let d=c[0][0],l=Ab(d);l===d||32>l.charCodeAt(0)||(M(3,"HE",c[0][1],d.length,0).b["char"]=l)})},w:"&",x:/&(?:[a-z]+|#(?:[0-9]+|x[0-9a-f]+));/ig,y:5E4},Litedown:{u:function(a){function b(e){-1<e.indexOf("&")&&(e=Ab(e));e=e.replace(/\\x1A/g,"");p&&(e=e.replace(/\\x1B./g,function(h){return{"\\u001b0":"!","\\u001b1":\'"\',"\\u001b2":"\'","\\u001b3":"(","\\u001b4":")","\\u001b5":"*","\\u001b6":"<","\\u001b7":">","\\u001b8":"[","\\u001b9":"\\\\","\\u001bA":"]","\\u001bB":"^","\\u001bC":"_",
"\\u001bD":"`","\\u001bE":"~"}[h]}));return e}function c(e){return 0<" abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".indexOf(e)}function d(e){a=a.substring(0,e)+"\\u0017"+a.substring(e+1)}function l(e,h){0<h&&(a=a.substring(0,e)+Array(1+h).join("\\u001a")+a.substring(e+h))}function k(e,h,r){if(-1!==a.indexOf(e))for(var q;q=h.exec(a);)e=q.index,q=e+q[0].length-2,O(r,e,2,q,2),l(e,2),l(q,2)}function g(e,h,r,q){function f(n){n=a.indexOf(h+"(",n);if(-1!==n){var t,y=!1;for(q.lastIndex=n;t=
q.exec(a);)y=t.index,t=t[0].length,O(e,y,2,y+t-1,1),l(y,t),y=!0;y&&f(n)}}let m=a.indexOf(h);-1!==m&&(function(n){var t;for(r.lastIndex=n;t=r.exec(a);){n=t[0];t=t.index;let y=n.length;n=n.charAt(y-1)===h?1:0;O(e,t,1,t+y-n,n)}}(m),f(m))}function v(e,h,r){var q=h.replace(/^\\s*/,"").replace(/\\s*$/,"");h="";let f=q.indexOf(" ");-1!==f&&(h=q.substring(f).replace(/^\\s*\\S/,"").replace(/\\S\\s*$/,""),q=q.substring(0,f));/^<.+>$/.test(q)&&(q=q.replace(/^<(.+)>$/,"$1").replace(/\\\\>/g,">"));q=b(q);e.b[r]=q;""<
h&&(r=b(h),e.b.title=r)}let p=!1,u=!1,w={};0<=a.indexOf("\\\\")&&(p=!0,a=a.replace(/\\\\[!"\'()*<>[\\\\\\]^_`~]/g,function(e){return{"\\\\!":"\\u001b0",\'\\\\"\':"\\u001b1","\\\\\'":"\\u001b2","\\\\(":"\\u001b3","\\\\)":"\\u001b4","\\\\*":"\\u001b5","\\\\<":"\\u001b6","\\\\>":"\\u001b7","\\\\[":"\\u001b8","\\\\\\\\":"\\u001b9","\\\\]":"\\u001bA","\\\\^":"\\u001bB","\\\\_":"\\u001bC","\\\\`":"\\u001bD","\\\\~":"\\u001bE"}[e]}));a+="\\n\\n\\u0017";(function(){function e(m,n){Hb(Qb("LIST",n,0),m.V);Hb(Qb("LI",n,0),m.I);m.Q&&m.J.forEach(t=>{t.flags&=-9})}function h(m,
n){let t=m;for(;0<=--n;)t=t.replace(/^ *>!? ?/,"");return m.length-t.length}function r(m){let n=[],t=/>!?/g,y;for(;y=t.exec(m);)n.push(y[0]);return n}function q(){if(-1!==a.indexOf("-")||-1!==a.indexOf("="))for(var m,n=/^(?=[-=>])(?:>!? ?)*(?=[-=])(?:-+|=+) *$/gm;m=n.exec(a);){let t=m[0];m=m.index;let y=m-1;for(;0<y&&" "===a[y-1];)--y;f[m-1]={M:m+t.length-y,N:y,T:t.length-t.replace(/>/g,"").length,K:"="===t[0]?"H1":"H2"}}}let f={};(function(){q();let m=[],n=0,t,y=4,z,N=!0,E=[],K=0,aa=!1,T=0,na,ia,
R,kb,$b,U,P,lb,Sa,ja,ka,qa,ac=[],mb,Ta=/^(?:(?=[-*+\\d \\t>`~#_])((?: {0,3}>(?:(?!!)|!(?![^\\n>]*?!<)) ?)+)?([ \\t]+)?(\\* *\\* *\\*[* ]*$|- *- *-[- ]*$|_ *_ *_[_ ]*$)?((?:[-*+]|\\d+\\.)[ \\t]+(?=\\S))?[ \\t]*(#{1,6}[ \\t]+|```+[^`\\n]*$|~~~+[^~\\n]*$)?)?/gm;for(;mb=Ta.exec(a);)ac.push(mb),mb.index===Ta.lastIndex&&++Ta.lastIndex;ac.forEach(D=>{var ba=[];let V=D.index;var ra=D[0].length,Q;ja=R=0;ia=!N;U=a.indexOf("\\n",V);N=U===V+ra&&!D[3]&&!D[4]&&!D[5];ra||++Ta.lastIndex;na=N&&ia;D[1]&&(ba=r(D[1]),ja=ba.length,R=
D[1].length,z&&"blockDepth"in z.b&&(ja=Math.min(ja,z.b.blockDepth),R=h(D[1],ja)),l(V,R));if(ja<n&&!ia){aa=!0;do{var W=m.pop();Hb(Qb(W.name,T,0),W)}while(ja<--n)}if(ja>n&&!N){aa=!0;do m.push(M(1,">!"===ba[n]?"SPOILER":"QUOTE",V,0,-999));while(ja>++n)}W=Q=0;if(D[2]&&!t){kb=D[2];$b=kb.length;do" "===kb[W]?++Q:Q=Q+4&-4;while(++W<$b&&Q<y)}z&&!t&&Q<y&&!N&&(aa=!0);aa&&(aa=!1,z&&(T>z.j?(l(z.j,T-z.j),Hb(z,Qb("CODE",T,0,-1))):F(z),t=z=null),E.forEach(Ba=>{e(Ba,T)}),E=[],K=0,V&&d(V-1));if(Q>=y){if(z||!ia)R=
(D[1]||"").length+W,z||=M(1,"CODE",V+R,0,-999),D={}}else if(!z){ba=!!D[4];if(Q||ia||ba)if(ia&&!ba)P=K-1;else if(K)for(P=0;P<K&&Q>E[P].P;)++P;else P=ba?0:-1;else P=-1;for(;P<K-1;)e(E.pop(),T),--K;P!==K||ba||--P;if(ba&&0<=P)if(na=!0,ka=V+R+W,qa=D[4].length,W=M(1,"LI",ka,qa,0),l(ka,qa),P<K)Hb(Qb("LI",T,0),E[P].I),E[P].I=W,E[P].J.push(W);else{++K;P?(Sa=E[P-1].P+1,lb=Math.max(Sa,4*P)):(Sa=0,lb=Q);Q=M(1,"LIST",ka,0,0);if(-1<D[4].indexOf(".")){Q.b.type="decimal";let Ba=+D[4];1!==Ba&&(Q.b.start=Ba)}E.push({V:Q,
I:W,J:[W],ea:Sa,P:lb,Q:!0})}!K||ia||N||(1<E[0].J.length||!ba)&&E.forEach(Ba=>{Ba.Q=!1});y=4*(K+1)}if(D[5])if("#"===D[5][0])W=D[5].length,ba=V+ra-W,Q=/[ \\t]*#*[ \\t]*$/.exec(a.substring(V+ra,U))[0].length,ra=U-Q,O("H"+/#{1,6}/.exec(D[5])[0].length,ba,W,ra,Q),d(ba),d(U),ia&&(na=!0);else{if("`"===D[5][0]||"~"===D[5][0])ka=V+R,qa=U-ka,z&&D[5]===t?(Hb(z,Qb("CODE",ka,qa,-1)),Rb(T,ka-T),l(z.j,ka+qa-z.j),t=z=null):z||(z=M(1,"CODE",ka,qa,0),t=D[5].replace(/[^`~]+/,""),z.b.blockDepth=ja,Rb(ka+qa,1),D=D[5].replace(/^[`~\\s]*/,
"").replace(/\\s+$/,""),""!==D&&(z.b.lang=D))}else D[3]&&!K&&"\\u0017"!==a[V+ra]?(M(3,"HR",V+R,ra-R,0),na=!0,d(U)):!f[U]||f[U].T!==ja||N||K||z||(O(f[U].K,V+R,0,f[U].N,f[U].M),d(f[U].N+f[U].M));na&&(M(3,"pb",T,0,0),d(T));N||(T=U);R&&Rb(V,R,1E3)})})()})();(function(){if(!(0>a.indexOf("]:")))for(var e,h=/^\\x1A* {0,3}\\[([^\\x17\\]]+)\\]: *([^[\\s\\x17]+ *(?:"[^\\x17]*?"|\'[^\\x17]*?\'|\\([^\\x17)]*\\))?) *(?=$|\\x17)\\n?/gm;e=h.exec(a);){Rb(e.index,e[0].length);let r=e[1].toLowerCase();w[r]||(u=!0,w[r]=e[2])}})();(function(){var e=
a.indexOf("`");if(0>e)var h=[];else{h=/(`+)(\\s*)[^\\x17`]*/g;var r=0,q=[],f=a.replace(/\\x1BD/g,"\\\\`");for(h.lastIndex=e;e=h.exec(f);)q.push({j:e.index,k:e[1].length,R:r,Y:e[2].length,next:e.index+e[0].length}),r=e[0].length-e[0].replace(/\\s+$/,"").length;h=q}f=-1;for(r=h.length;++f<r-1;)for(e=h[f].next,q=f,"`"!==a[h[f].j]&&(++h[f].j,--h[f].k);++q<r&&h[q].j===e;){if(h[q].k===h[f].k){f=h[f];var m=h[q];e=f.j;let n=m.j-m.R;m=m.k+m.R;O("C",e,f.k+f.Y,n,m);l(e,n+m-e);f=q;break}e=h[q].next}})();(function(){function e(h,
r,q,f,m){let n=O("IMG",h,2,r,q);v(n,f,"src");f=b(m);n.b.alt=f;l(h,r+q-h)}(function(){var h=a.indexOf("![");if(-1!==h){if(0<a.indexOf("](",h))for(var r=/!\\[(?:[^\\x17[\\]]|\\[[^\\x17[\\]]*\\])*\\]\\(( *(?:[^\\x17\\s()]|\\([^\\x17\\s()]*\\))*(?=[ )]) *(?:"[^\\x17]*?"|\'[^\\x17]*?\'|\\([^\\x17)]*\\))? *)\\)/g;h=r.exec(a);){var q=h[1],f=h.index,m=3+q.length;e(f,f+h[0].length-m,m,q,h[0].substring(2,h[0].length-m))}if(u)for(r=/!\\[((?:[^\\x17[\\]]|\\[[^\\x17[\\]]*\\])*)\\](?: ?\\[([^\\x17[\\]]+)\\])?/g;h=r.exec(a);){q=h.index;f=q+2+h[1].length;
m=1;let n=h[1],t=n;if(""<h[2]&&w[h[2]])m=h[0].length-n.length-2,t=h[2];else if(!w[t])continue;e(q,f,m,w[t],n)}}})()})();k(">!",/>![^\\x17]+?!</g,"ISPOILER");k("||",/\\|\\|[^\\x17]+?\\|\\|/g,"ISPOILER");(function(){function e(f,m,n,t){let y=O("URL",f,1,m,n,1===n?1:-1);v(y,t,"url");l(f,1);l(m,n)}function h(){let f,m=/<[-+.\\w]+([:@])[^\\x17\\s>]+?(?:>|\\x1B7)/g;for(;f=m.exec(a);){let n=b(f[0].replace(/\\x1B/g,"\\\\\\u001b")).replace(/^<(.+)>$/,"$1"),t=f.index,y=":"===f[1]?"URL":"EMAIL",z=y.toLowerCase();O(y,t,1,
t+f[0].length-1,1).b[z]=n}}function r(){let f,m=/\\[(?:[^\\x17[\\]]|\\[[^\\x17[\\]]*\\])*\\]\\(( *(?:\\([^\\x17\\s()]*\\)|[^\\x17\\s)])*(?=[ )]) *(?:"[^\\x17]*?"|\'[^\\x17]*?\'|\\([^\\x17)]*\\))? *)\\)/g;for(;f=m.exec(a);){let n=f[1],t=f.index,y=3+n.length;e(t,t+f[0].length-y,y,n)}}function q(){let f={};for(var m,n=/\\[((?:[^\\x17[\\]]|\\[[^\\x17[\\]]*\\])*)\\]/g;m=n.exec(a);)f[m.index]=m[1].toLowerCase();let t;for(t in f){m=f[t];n=+t+2+m.length;let y=n-1,z=1;" "===a[n]&&++n;""<f[n]&&w[f[n]]&&(m=f[n],z=n+2+m.length-y);w[m]&&e(+t,
y,z,w[m])}}-1!==a.indexOf("](")&&r();-1!==a.indexOf("<")&&h();u&&q()})();k("~~",/~~[^\\x17]+?~~(?!~)/g,"DEL");g("SUB","~",/~[^\\x17\\s!"#$%&\'()*+,\\-.\\/:;<=>?@[\\]^_`{}|~]+~?/g,/~\\([^\\x17()]+\\)/g);g("SUP","^",/\\^[^\\x17\\s!"#$%&\'()*+,\\-.\\/:;<=>?@[\\]^_`{}|~]+\\^?/g,/\\^\\([^\\x17()]+\\)/g);(function(){function e(z,N){var E=a.indexOf(z);if(-1!==E){z=[];let na=[],ia=a.indexOf("\\u0017",E);var K;for(N.lastIndex=E;K=N.exec(a);){E=K.index;let R=K[0].length;E>ia&&(na.push(z),z=[],ia=a.indexOf("\\u0017",E));var aa=E,T=
R;if(K="_"===a.charAt(aa)&&1===T)K=0<aa&&c(a[aa-1])&&c(a[aa+T]);K||z.push([E,R])}na.push(z);na.forEach(h)}}function h(z){t=f=-1;z.forEach(N=>{var E=N[0];N=N[1];let K=!(-1<" \\n\\t".indexOf(a[E+N-1+1])),aa=0<E&&-1<" \\n\\t".indexOf(a.charAt(E-1))?0:Math.min(N,3);r=!!(aa&1)&&0<=f;q=!!(aa&2)&&0<=t;y=m=E;n=N;0<=f&&f===t&&(r?f+=2:++t);r&&q&&(f<t?m+=2:++y);r&&(--n,O("EM",f,1,m,1),f=-1);q&&(n-=2,O("STRONG",t,2,y,2),t=-1);n=K?Math.min(n,3):0;E+=N;n&1&&(f=E-n);n&2&&(t=E-n)})}let r,q,f,m,n,t,y;e("*",/\\*+/g);e("_",
/_+/g)})();(function(){let e=a.indexOf("  \\n");for(;0<e;)Gb(M(3,"br",e+2,0,0),M(3,"v",e+2,1,0)),e=a.indexOf("  \\n",e+3)})()}},MediaEmbed:{u:function(a,b){b.forEach(c=>{let d=c[0][0];M(3,"MEDIA",c[0][1],d.length,-10).b.url=d})},w:"://",x:/\\bhttps?:\\/\\/[^["\'\\s]+/ig,y:5E4},PipeTables:{u:function(a){function b(w,e){k=e.j;e.G.split("|").forEach((h,r)=>{0<r&&(Gb(v,Rb(k,1,1E3)),++k);r=g.L[r]?g.L[r]:"";var q=k,f=q+h.length;k=f;let m=/^( *).*?( *)$/.exec(h);m[1]&&(h=m[1].length,Gb(v,Rb(q,h,1E3)),q+=h);m[2]&&
(h=m[2].length,Gb(v,Rb(f-h,h,1E3)),f-=h);f=q===f?M(3,w,q,0,-101):O(w,q,0,f,0,-101);r&&(f.b.align=r)});O("TR",e.j,0,k,0,-102)}function c(){if(g&&2<g.o.length&&/^ *:?-+:?(?:(?:\\+| *\\| *):?-+:?)+ */.test(g.o[1].G)){var w=g,e=g.o[1].G;let h=["","right","left","center"],r=[],q=/(:?)-+(:?)/g,f;for(;f=q.exec(e);)r.push(h[(f[1]?2:0)+(f[2]?1:0)]);w.L=r;p.push(g)}g=null}function d(w){return w.replace(/[!>]/g," ")}function l(w){return w.replace(/\\|/g,".")}let k,g=null,v,p,u=a;-1<u.indexOf("`")&&(u=u.replace(/`[^`]*`/g,
l));-1<u.indexOf(">")&&(u=u.replace(/^(?:>!? ?)+/gm,d));-1<u.indexOf("\\\\|")&&(u=u.replace(/\\\\[\\\\|]/g,".."));(function(){g=null;p=[];k=0;u.split("\\n").forEach(w=>{if(0>w.indexOf("|"))c();else{var e=w;let h=0;g||(g={o:[]},h=/^ */.exec(e)[0].length,e=e.substring(h));e=e.replace(/^( *)\\|/,"$1 ").replace(/\\|( *)$/," $1");g.o.push({G:e,j:k+h})}k+=1+w.length});c()})();(function(){let w=-1,e=p.length;for(;++w<e;){g=p[w];var h=g.o[g.o.length-1];v=O("TABLE",g.o[0].j,0,h.j+h.G.length,0,-104);b("TH",g.o[0]);
O("THEAD",g.o[0].j,0,k,0,-103);h=g.o[1];Gb(v,Rb(h.j-1,1+h.G.length,1E3));h=1;let r=g.o.length;for(;++h<r;)b("TD",g.o[h]);O("TBODY",g.o[2].j,0,k,0,-103)}})()},w:"|"}};let S;
const Ra={"MediaEmbed.hosts":{"bandcamp.com":"bandcamp","dai.ly":"dailymotion","dailymotion.com":"dailymotion","facebook.com":"facebook","fb.watch":"facebook","link.tospotify.com":"spotify","liveleak.com":"liveleak","open.spotify.com":"spotify","play.spotify.com":"spotify","soundcloud.com":"soundcloud","spotify.link":"spotify","twitch.tv":"twitch","vimeo.com":"vimeo","vine.co":"vine","youtu.be":"youtube","youtube-nocookie.com":"youtube","youtube.com":"youtube"},"MediaEmbed.sites":{bandcamp:[[],[{v:[[/\\/album=(\\d+)/,
Ca]],match:[[/bandcamp\\.com\\/album\\/./,ca]]},{v:[[/"album_id":(\\d+)/,Ca],[/"track_num":(\\d+)/,["","track_num"]],[/\\/track=(\\d+)/,Da]],match:[[/bandcamp\\.com\\/track\\/./,ca]]}]],dailymotion:[[[/dai\\.ly\\/([a-z0-9]+)/i,x],[/dailymotion\\.com\\/(?:live\\/|swf\\/|user\\/[^#]+#video=|(?:related\\/\\d+\\/)?video\\/)([a-z0-9]+)/i,x],[/start=(\\d+)/,ea]],[]],facebook:[[[/facebook\\.com\\/.*?(?:fbid=|\\/permalink\\/|\\?v=)(\\d+)/,x],[/facebook\\.com\\/([.\\w]+)\\/([pv])(?:ost|ideo)s?\\/(\\d+)/,Ga],[/facebook\\.com\\/video\\/(?=post|video)([pv])/,
ha],[/facebook\\.com\\/events\\/(\\d+)\\b(?!\\/permalink)/,x],[/facebook\\.com\\/watch\\/\\?([pv])=/,ha],[/facebook.com\\/groups\\/[^\\/]*\\/(p)osts\\/(\\d+)/,Ea],[/facebook\\.com\\/([.\\w]+)\\/posts\\/pfbid(\\w+)/,["","user","pfbid"]],[/facebook\\.com\\/permalink\\.php\\?story_fbid=(?:(\\d+)|pfbid(\\w+))&id=(\\d+)/,["","id","pfbid","page_id"]],[/facebook\\.com\\/([.\\w]+)\\/(v)ideos\\/[^\\/]+\\/(\\d+)\\//,Ga],[/facebook\\.com\\/(r)eel\\/(\\d+)/,Ea]],[{v:[[/facebook\\.com\\/([.\\w]+)\\/([pv])\\w+\\/(\\d+)(?!\\w)/,Ga]],B:"User-agent: PHP (not Mozilla)",
match:[[/facebook\\.com\\/[.\\w]+\\/posts\\/pfbid/,ca]],Z:"https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2F{@user}%2Fposts%2Fpfbid{@pfbid}"},{v:[[/story_fbid=(\\d+)/,x]],B:"User-agent: PHP (not Mozilla)",match:[[/facebook\\.com\\/permalink\\.php\\?story_fbid=pfbid(\\w+)&id=(\\d+)/,["","pfbid","page_id"]]],Z:"https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2Fpermalink.php%3Fstory_fbid%3Dpfbid{@pfbid}%26id%3D{@page_id}"},{v:[[/facebook\\.com\\/watch\\/\\?(v)=(\\d+)/,
Ea],[/facebook\\.com\\/([.\\w]+)\\/(v)ideos\\/(\\d+)/,Ga]],B:"User-agent: PHP (not Mozilla)",match:[[/fb\\.watch\\/./,ca]]}]],liveleak:[[[/liveleak\\.com\\/(?:e\\/|view\\?i=)(\\w+)/,x]],[{v:[[/liveleak\\.com\\/e\\/(\\w+)/,x]],match:[[/liveleak\\.com\\/view\\?t=/,ca]]}]],soundcloud:[[[/https?:\\/\\/(?:api\\.)?soundcloud\\.com\\/(?!pages\\/)([-\\/\\w]+\\/[-\\/\\w]+|^[^\\/]+\\/[^\\/]+$)/i,x],[/api\\.soundcloud\\.com\\/playlists\\/(\\d+)/,Fa],[/api\\.soundcloud\\.com\\/tracks\\/(\\d+)(?:\\?secret_token=([-\\w]+))?/,["","track_id","secret_token"]],
[/soundcloud\\.com\\/(?!playlists\\/|tracks\\/)[-\\w]+\\/(?:sets\\/)?[-\\w]+\\/(?=s-)([-\\w]+)/,["","secret_token"]]],[{v:[[/soundcloud(?::\\/)?:tracks:(\\d+)/,Da]],B:"User-agent: PHP (not Mozilla)",match:[[/soundcloud\\.com\\/(?!playlists\\/\\d|tracks\\/\\d)[-\\w]+\\/[-\\w]/,ca]]},{v:[[/soundcloud(?::\\/)?\\/playlists:(\\d+)/,Fa]],B:"User-agent: PHP (not Mozilla)",match:[[/soundcloud\\.com\\/[-\\w]+\\/sets\\//,ca]]}]],spotify:[La,[{v:La,B:"User-agent: PHP (not Mozilla)",match:[[/https?:\\/\\/(?:link\\.tospotify\\.com|spotify\\.link)\\/./,
ca]]}]],twitch:[[[/twitch\\.tv\\/(?:videos|\\w+\\/v)\\/(\\d+)?/,["","video_id"]],[/www\\.twitch\\.tv\\/(?!videos\\/)(\\w+)(?:\\/clip\\/([-\\w]+))?/,Ha],[/t=((?:(?:\\d+h)?\\d+m)?\\d+s)/,ea],[/clips\\.twitch\\.tv\\/(?:(\\w+)\\/)?([-\\w]+)/,Ha]],[]],vimeo:[[[/vimeo\\.com\\/(?:channels\\/[^\\/]+\\/|video\\/)?(\\d+)(?:\\/(\\w+))?\\b/,["","id","h"]],[/#t=([\\dhms]+)/,ea]],[]],vine:[[[/vine\\.co\\/v\\/([^\\/]+)/,x]],[]],youtube:[[[/youtube\\.com\\/(?:watch.*?v=|(?:embed|live|shorts|v)\\/|attribution_link.*?v%3D)([-\\w]+)/,x],[/youtube-nocookie\\.com\\/embed\\/([-\\w]+)/,
x],[/youtu\\.be\\/([-\\w]+)/,x],[/[#&?]t(?:ime_continue)?=(\\d[\\dhms]*)/,ea],[/[&?]list=([-\\w]+)/,["","list"]]],[{v:[[/\\/embed\\/([-\\w]+)\\?clip=([-\\w]+)&amp;clipt=([-\\w]+)/,["","id","clip","clipt"]]],match:[[/youtube\\.com\\/clip\\/./,ca]]}]]},urlConfig:{S:/^(?:ftp|https?|mailto)$/i}},Tb={d:pa,flags:8},Y={BANDCAMP:{d:sa,b:{album_id:A,track_id:A,track_num:A},i:2,c:G,f:10,e:ma,g:5E3},C:db,CODE:{d:da,b:{lang:Za},i:1,c:G,f:10,e:{m:B,flags:4436,n:B},g:5E3},DAILYMOTION:{d:sa,b:{id:A,t:A},i:2,c:G,f:10,e:ma,g:5E3},
DEL:gb,EM:eb,EMAIL:{d:ua,b:{email:{c:[function(a){return/^[-\\w.+]+@[-\\w.]+$/.test(a)?a:!1}],q:!0}},i:2,c:G,f:10,e:la,g:5E3},ESC:{d:da,b:{},i:7,c:G,f:10,e:{flags:1616},g:5E3},FACEBOOK:{d:sa,b:{id:A,page_id:A,pfbid:A,type:A,user:A},i:2,c:G,f:10,e:ma,g:5E3},FP:ob,H1:qb,H2:qb,H3:qb,H4:qb,H5:qb,H6:qb,HC:{d:da,b:{content:Ia},i:7,c:G,f:10,e:{flags:3153},g:5E3},HE:ob,HR:{d:va,b:{},i:1,c:G,f:10,e:{m:B,flags:3349},g:5E3},IMG:{d:va,b:{alt:A,src:$a,title:A},i:0,c:G,f:10,e:ma,g:5E3},ISPOILER:fb,LI:{d:wa,b:{},
i:4,c:[Ma,function(a){for(var b=a.j+a.k;" "===X.charAt(b);)++b;var c=X.substring(b,b+3);if(/\\[[ Xx]\\]/.test(c)){var d=Math.random().toString(16).substring(2);c="[ ]"===c?"unchecked":"checked";b=M(3,"TASK",b,3,0);b.b.id=d;b.b.state=c;Gb(a,b)}}],f:10,e:hb,g:5E3},LIST:{d:xa,b:{start:{c:[function(a){return/^(?:0|[1-9]\\d*)$/.test(a)?a:!1}],q:!1},type:Za},i:1,c:G,f:10,e:bb,g:5E3},LIVELEAK:nb,MEDIA:{d:[65519,65329],b:{},i:15,c:[function(a){return function(b,c,d){function l(k,g,v){let p=!1;v.forEach(u=>{let w=
u[1],e=u[0].exec(g);e&&(p=!0,w.forEach((h,r)=>{""<e[r]&&""<h&&(k[h]=e[r])}))});return p}(function(k,g,v){F(k);if("url"in k.b){var p=k.b.url;a:{for(var u=/^https?:\\/\\/([^\\/]+)/.exec(p.toLowerCase())[1]||"";""<u;){if(g[u]){g=g[u];break a}u=u.replace(/^[^.]*./,"")}g=""}if(v[g]){u={};l(u,p,v[g][0]);v=u;a:{for(w in v){var w=!1;break a}w=!0}if(!w){w=k.j;var e=k.A;e?(p=k.k,u=e.j,e=e.k):(p=0,u=k.j+k.k,e=0);k=O(g.toUpperCase(),w,p,u,e,k.r);Na(k,v)}}}})(b,c,d)}(a,Ra["MediaEmbed.hosts"],Ra["MediaEmbed.sites"],
Ra.cacheDir)}],f:10,e:{flags:513},g:5E3},QUOTE:{d:wa,b:{},i:1,c:G,f:10,e:ab,g:5E3},SOUNDCLOUD:{d:sa,b:{id:A,playlist_id:A,secret_token:A,track_id:A},i:2,c:G,f:10,e:ma,g:5E3},SPOILER:{d:wa,b:{},i:5,c:G,f:10,e:ab,g:5E3},SPOTIFY:nb,STRONG:eb,SUB:fb,SUP:fb,TABLE:rb,TASK:{d:va,b:{id:Ya,state:Ya},i:2,c:G,f:10,e:ma,g:5E3},TBODY:wb,TD:{d:wa,b:pb,i:10,c:G,f:10,e:jb,g:5E3},TH:{d:za,b:pb,i:10,c:G,f:10,e:jb,g:5E3},THEAD:sb,TR:vb,TWITCH:{d:sa,b:{channel:A,clip_id:A,t:A,video_id:A},i:2,c:G,f:10,e:ma,g:5E3},URL:{d:ua,
b:{title:A,url:$a},i:6,c:G,f:10,e:la,g:5E3},VIMEO:{d:sa,b:{h:A,id:A,t:Qa},i:2,c:G,f:10,e:ma,g:5E3},VINE:nb,YOUTUBE:{d:sa,b:{clip:A,clipt:A,id:{c:Pa,q:!1},list:A,t:Qa},i:2,c:G,f:10,e:ma,g:5E3},"html:abbr":{d:ta,b:{title:A},i:0,c:G,f:10,e:fa,g:5E3},"html:b":eb,"html:br":{d:Aa,b:{},i:0,c:G,f:10,e:oa,g:5E3},"html:code":db,"html:dd":{d:wa,b:{},i:12,c:G,f:10,e:ib,g:5E3},"html:del":gb,"html:div":{d:pa,b:Ja,i:13,c:G,f:10,e:ab,g:5E3},"html:dl":{d:[65408,65456],b:{},i:1,c:G,f:10,e:bb,g:5E3},"html:dt":{d:za,
b:{},i:12,c:G,f:10,e:ib,g:5E3},"html:i":eb,"html:img":{d:Aa,b:{alt:A,height:A,src:{c:Xa,q:!1},title:A,width:A},i:0,c:G,f:10,e:oa,g:5E3},"html:ins":gb,"html:li":{d:wa,b:{},i:4,c:G,f:10,e:hb,g:5E3},"html:ol":tb,"html:pre":{d:ta,b:{},i:1,c:G,f:10,e:{m:B,flags:276,n:B},g:5E3},"html:rb":ub,"html:rp":{d:va,b:{},i:14,c:G,f:10,e:{m:Ka,flags:3344,n:B},g:5E3},"html:rt":{d:ta,b:{},i:14,c:G,f:10,e:{m:Ka,flags:256,n:B},g:5E3},"html:rtc":ub,"html:ruby":{d:[65477,65473],b:{},i:0,c:G,f:10,e:fa,g:5E3},"html:span":{d:ta,
b:Ja,i:0,c:G,f:10,e:fa,g:5E3},"html:strong":eb,"html:sub":fb,"html:sup":fb,"html:table":rb,"html:tbody":wb,"html:td":{d:wa,b:{colspan:A,rowspan:A},i:10,c:G,f:10,e:jb,g:5E3},"html:tfoot":wb,"html:th":{d:za,b:{colspan:A,rowspan:A,scope:A},i:10,c:G,f:10,e:jb,g:5E3},"html:thead":sb,"html:tr":vb,"html:u":eb,"html:ul":tb};let Z,Ub,X,Vb,Wb=0,Xb;
function Yb(a){a=a.replace(/\\r\\n?/g,"\\n");a=a.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\uFFFE\\uFFFF]/g,"");var b=C;b.r=[];delete b.z;delete b.l;Lb={};Mb={};Nb=0;I=null;Ob=!1;Pb={};J=[];L="";S=0;Z=[];Ub=!1;X=a;Vb=X.length;Xb=0;H=Tb;H.D=!1;++Wb;a=Wb;for(var c in Sb)if(!Sb[c].F)a:{b=c;var d=Sb[b];if(!(d.w&&0>X.indexOf(d.w))){var l=[];if("undefined"!==typeof d.x&&"undefined"!==typeof d.y){let k=void 0;l=d.x;d=d.y;l.lastIndex=0;let g=[],v=0;for(;++v<=d&&(k=l.exec(X));){let p=k.index,u=[[k[0],p]],w=0;for(;++w<
k.length;){let e=k[w];void 0===e?u.push(["",-1]):(u.push([e,X.indexOf(e,p)]),p+=e.length)}g.push(u)}l=g;if(!l.length)break a}(0,Sb[b].u)(X,l)}}Zb();bc(Vb,0,!0);do c=L,L=L.replace(/<([^ />]+)[^>]*><\\/\\1>/g,"");while(L!==c);L=L.replace(/<\\/i><i>/g,"");L=L.replace(/[\\x00-\\x08\\x0B-\\x1F\\uFFFE\\uFFFF]/g,"");L=L.replace(/[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g,cc);b=Ob?"r":"t";c="<"+b;for(let k in Pb)c+=" xmlns:"+k+\'="urn:s9e:TextFormatter:\'+k+\'"\';L=c+">"+L+"</"+b+">";if(Wb!==a)throw"The parser has been reset during execution";
1E4<Nb&&C.add("warn","Fixing cost limit exceeded",void 0);return L}function dc(a){let b={},c;for(c in Y[a])b[c]=Y[a][c];return Y[a]=b}function cc(a){return"&#"+((a.charCodeAt(0)<<10)+a.charCodeAt(1)-56613888)+";"}
function ec(a){Ob=!0;let b=a.name,c=a.j,d=a.k;var l=a.flags,k=0;let g=0;l&256&&(k=1,g=a.l&2?2:1);bc(c,k,!(a.l&1&&!(l&4)));k=d?Cb(X.substring(c,c+d)):"";if(a.l&1){l&4||fc(c);l=b.indexOf(":");0<l&&(Pb[b.substring(0,l)]=0);L+="<"+b;let v=Jb(a);l=[];for(let p in v)l.push(p);l.sort((p,u)=>p>u?1:-1);l.forEach(p=>{L+=" "+p+\'="\'+Bb(v[p].toString()).replace(/\\n/g,"&#10;")+\'"\'});L=3===a.l?d?L+(">"+k+"</"+b+">"):L+"/>":d?L+("><s>"+k+"</s>"):L+">"}else d&&(L+="<e>"+k+"</e>"),L+="</"+b+">";for(Xb=S=c+d;g&&Xb<
Vb&&"\\n"===X[Xb];)--g,++Xb}
function bc(a,b,c){c&&(H.flags&8?b=-1:c=!1);S>=a&&c&&gc();if(Xb>S){var d=Math.min(a,Xb);L+=X.substring(S,d);S=d;S>=a&&c&&gc()}if(H.flags&128)b=X.substring(S,a),/^[ \\n\\t]*$/.test(b)||(b="<i>"+Cb(b)+"</i>"),L+=b,S=a,c&&gc();else{var l=a;for(d=0;b&&--l>=S;){let k=X[l];if(" "!==k&&"\\n"!==k&&"\\t"!==k)break;"\\n"===k&&--b;++d}a-=d;if(H.flags&8)for(H.D||(hc(a),a>S&&fc(a)),b=X.indexOf("\\n\\n",S);-1<b&&b<a;)bc(b,0,!0),fc(a),b=X.indexOf("\\n\\n",S);a>S&&(b=Cb(X.substring(S,a)),L+=b);c&&gc();d&&(L+=X.substring(a,
a+d));S=a+d}}function ic(a){let b=a.j;a=a.k;let c=X.substring(b,b+a);bc(b,0,!1);L+="<i>"+Cb(c)+"</i>";Ob=!0;S=b+a}function fc(a){!H.D&&H.flags&8&&(hc(a),S<Vb&&(L+="<p>",H.D=!0))}function gc(){H.D&&(L+="</p>",H.D=!1)}function hc(a){for(;S<a&&-1<" \\n\\t".indexOf(X[S]);)L+=X[S],++S}function jc(a,b,c){let d=a.name;(I.flags|a.flags)&256&&(b=kc(b));b=Qb(d,b,0,c||0);Hb(b,a)}function kc(a){for(;a>S&&-1<" \\n\\t".indexOf(X[a-1]);)--a;return a}
function Zb(){if(Z.length){for(let a in Y)Lb[a]=0,Mb[a]=0;do{for(;Z.length;)Ub||lc(),I=Z.pop(),mc();J.forEach(a=>{jc(a,Vb)})}while(Z.length)}}
function mc(){H.flags&64&&!Kb(I,J[J.length-1])&&!(-1<"br i pb v".indexOf(I.name))&&F(I);var a=I.j,b=I.k;if(S>a&&!I.p){var c;if((c=I.H)&&0<=J.indexOf(c)){Hb(Qb(c.name,S,Math.max(0,a+b-S)),c);return}if("i"===I.name&&(a=a+b-S,0<a)){Rb(S,a);return}F(I)}if(!I.p)if("i"===I.name)ic(I);else if("br"===I.name)H.flags&1024||(bc(I.j,0,!1),L+="<br/>");else if("pb"===I.name)bc(I.j,0,!0);else if("v"===I.name)a=H.flags,H.flags=I.flags,bc(I.j+I.k,0,!1),H.flags=a;else if(I.l&1)if(a=I,b=a.name,c=Y[b],Mb[b]>=c.g)C.add("err",
"Tag limit exceeded",{tag:a,tagName:b,tagLimit:c.g}),F(a);else{var d=a,l=Y[d.name];C.l=d;for(var k=0;k<l.c.length&&!d.p;++k)l.c[k](d,l);delete C.l;if(!(d=a.p)&&(d=1E4>Nb)){a:{d=a;if(J.length){k=d.name;var g=Y[k];if(g.e.n){l=J[J.length-1];let v=l.name;if(g.e.n[v]){if(v!==k&&1E4>Nb){k=d.j+d.k;Z.length?(g=Z[Z.length-1],g=g.j):g=Vb+1;for(;k<g&&-1<" \\n\\t".indexOf(X[k]);)++k;k=nc(l,k);Gb(d,k)}Z.push(d);jc(l,d.j,d.r-1);Nb+=4;d=!0;break a}}}d=!1}if(!d)a:{d=a;if(J.length&&(l=Y[d.name],l.e.m&&(k=J[J.length-
1],l.e.m[k.name]))){++Nb;Z.push(d);jc(k,d.j,d.r-1);d=!0;break a}d=!1}d=d||!1}d||(Lb[b]>=c.f?(C.add("err","Nesting limit exceeded",{tag:a,tagName:b,nestingLimit:c.f}),F(a)):(c=Y[b].i,H.d[c>>3]&1<<(c&7)?(!(a.flags&1&&3!==a.l)||a.A||Z.length&&Kb(Z[Z.length-1],a)||(b=new Fb(3,b,a.j,a.k),Na(b,Jb(a)),b.flags=a.flags,a=b),a.flags&4096&&"\\n"===X[a.j+a.k]&&Rb(a.j+a.k,1),ec(a),oc(a)):(b={tag:a,tagName:b},0<a.k?C.add("warn","Tag is not allowed in this context",b):Eb("Tag is not allowed in this context",b),F(a))))}else pc()}
function pc(){var a=I;if(Lb[a.name]){for(var b=[],c=J.length;0<=--c;){var d=J[c];if(Kb(a,d))break;b.push(d);++Nb}if(0>c)Eb("Skipping end tag with no start tag",{tag:a});else{var l=a.flags;b.forEach(p=>{l|=p.flags});var k=l&256,g=1E4>Nb,v=[];b.forEach(p=>{var u=p.name;g&&(p.flags&2?v.push(p):g=!1);let w=a.j;k&&(w=kc(w));u=new Fb(2,u,w,0);u.flags=p.flags;ec(u);qc()});ec(a);qc();if(b.length&&1E4>Nb){d=S;for(c=Z.length;0<=--c&&1E4>++Nb;){let p=Z[c];if(p.j>d||p.l&1)break;let u=b.length;for(;0<=--u&&1E4>
++Nb;)if(Kb(p,b[u])){b.splice(u,1);v[u]&&v.splice(u,1);d=Math.max(d,p.j+p.k);break}}d>S&&ic(new Fb(3,"i",S,d-S))}v.forEach(p=>{let u=nc(p,S);(p=p.A)&&Hb(u,p)})}}}function qc(){let a=J.pop();--Lb[a.name];H=H.W}function oc(a){let b=a.name,c=a.flags,d=Y[b];++Mb[b];if(3!==a.l){var l=[];H.d.forEach((g,v)=>{c&512||(g=g&65280|g>>8);l.push(d.d[v]&g)});var k=c|H.flags&32;k&16&&(k&=-33);++Lb[b];J.push(a);H={W:H};H.d=l;H.flags=k}}function Qb(a,b,c,d){return M(2,a,b,c,d||0)}
function Rb(a,b,c){return M(3,"i",a,Math.min(b,Vb-a),c||0)}function nc(a,b){b=M(a.l,a.name,b,0,a.r);Na(b,Jb(a));return b}
function M(a,b,c,d,l){a=new Fb(a,b,c,d,l||0);Y[b]&&(a.flags=Y[b].e.flags);if(!(Y[b]||-1<"br i pb v".indexOf(a.name))||0>d||0>c||c+d>Vb||/[\\uDC00-\\uDFFF]/.test(X.substring(c,c+1)+X.substring(c+d,c+d+1)))F(a);else if(Y[b]&&Y[b].F)C.add("warn","Tag is disabled",{tag:a,tagName:b}),F(a);else if(Ub){b=Z.length;for(c=rc(a);0<b&&c>rc(Z[b-1]);)Z[b]=Z[b-1],--b;Z[b]=a}else Z.push(a);return a}function O(a,b,c,d,l,k){d=Qb(a,d,l,-k||0);a=M(1,a,b,c,k||0);Hb(a,d);return a}
function lc(){let a={},b=[],c=Z.length;for(;0<=--c;){let d=Z[c],l=rc(d,c);b.push(l);a[l]=d}b.sort();c=b.length;for(Z=[];0<=--c;)Z.push(a[b[c]]);Ub=!0}function rc(a,b){let c=0<=a.r,d=a.r;c||(d+=1073741824);let l=0<a.k,k;l?k=Vb-a.k:k={2:0,3:1,1:2}[a.l];return sc(a.j)+ +c+sc(d)+ +l+sc(k)+sc(b||0)}function sc(a){a=a.toString(16);return"        ".substring(a.length)+a}function tc(a){const b=(new DOMParser).parseFromString(a,"text/xml");if(!b)throw"Cannot parse "+a;return b}
function uc(a,b){return vc.transformToFragment(tc(a),b)}var vc;vc=new XSLTProcessor;vc.importStylesheet(tc(xb));window.s9e||(window.s9e={});
window.s9e.TextFormatter={disablePlugin:function(a){Sb[a]&&(Sb[a].F=!0)},disableTag:function(a){Y[a]&&(dc(a).F=!0)},enablePlugin:function(a){Sb[a]&&(Sb[a].F=!1)},enableTag:function(a){Y[a]&&(dc(a).F=!1)},getLogger:function(){return C},parse:Yb,preview:function(a,b){function c(g,v){var p=g.childNodes;v=v.childNodes;var u=p.length,w=v.length;let e,h,r=0,q=0;for(;r<u&&r<w;){e=p[r];h=v[r];if(!d(e,h))break;++r}let f=Math.min(u-r,w-r);for(;q<f;){e=p[u-(q+1)];h=v[w-(q+1)];if(!d(e,h))break;++q}for(u-=q;--u>=
r;)g.removeChild(p[u]),k=g;p=w-q;if(!(r>=p)){w=l.createDocumentFragment();u=r;do h=v[u],k=w.appendChild(h);while(u<--p);q?g.insertBefore(w,g.childNodes[r]):g.appendChild(w)}}function d(g,v){if(g.nodeName!==v.nodeName||g.nodeType!==v.nodeType)return!1;if(g instanceof HTMLElement&&v instanceof HTMLElement){if(!g.isEqualNode(v)){var p=g.attributes;let h=v.attributes;var u=h.length;let r=p.length,q=" "+g.getAttribute("data-s9e-livepreview-ignore-attrs")+" ";for(;0<=--r;){var w=p[r],e=w.namespaceURI;w=
w.name;-1<q.indexOf(" "+w+" ")||v.hasAttributeNS(e,w)||(g.removeAttributeNS(e,w),k=g)}for(r=u;0<=--r;)e=h[r],p=e.namespaceURI,u=e.name,e=e.value,-1<q.indexOf(" "+u+" ")||e===g.getAttributeNS(p,u)||(g.setAttributeNS(p,u,e),k=g);c(g,v)}}else 3!==g.nodeType&&8!==g.nodeType||g.nodeValue===v.nodeValue||(g.nodeValue=v.nodeValue,k=g);return!0}let l=b.ownerDocument;if(!l)throw"Target does not have a ownerDocument";a=uc(Yb(a).replace(/<[eis]>[^<]*<\\/[eis]>/g,""),l);let k=b;"undefined"!==typeof window&&"chrome"in
window&&a.querySelectorAll("script").forEach(function(g){let v=document.createElement("script");for(let p of g.attributes)v.setAttribute(p.name,p.value);v.textContent=g.textContent;g.parentNode.replaceChild(v,g)});c(b,a);return k},registeredVars:Ra,setNestingLimit:function(a,b){Y[a]&&(dc(a).f=b)},setParameter:function(a,b){vc.setParameter(null,a,b)},setTagLimit:function(a,b){Y[a]&&(dc(a).g=b)}};})();';
	}

	/**
	* {@inheritdoc}
	*/
	public static function getParser()
	{
		return unserialize('O:24:"s9e\\TextFormatter\\Parser":4:{s:16:"' . "\0" . '*' . "\0" . 'pluginsConfig";a:10:{s:9:"Autoemail";a:5:{s:10:"quickMatch";s:1:"@";s:11:"regexpLimit";i:50000;s:8:"attrName";s:5:"email";s:6:"regexp";s:39:"/\\b[-a-z0-9_+.]+@[-a-z0-9.]*[a-z0-9]/Si";s:7:"tagName";s:5:"EMAIL";}s:8:"Autolink";a:5:{s:8:"attrName";s:3:"url";s:6:"regexp";s:135:"#\\b(?:ftp|https?|mailto):(?>[^\\s()\\[\\]\\x{FF01}-\\x{FF0F}\\x{FF1A}-\\x{FF20}\\x{FF3B}-\\x{FF40}\\x{FF5B}-\\x{FF65}]|\\([^\\s()]*\\)|\\[\\w*\\])++#Siu";s:7:"tagName";s:3:"URL";s:10:"quickMatch";s:1:":";s:11:"regexpLimit";i:50000;}s:7:"Escaper";a:4:{s:10:"quickMatch";s:1:"\\";s:11:"regexpLimit";i:50000;s:6:"regexp";s:30:"/\\\\[-!#()*+.:<>@[\\\\\\]^_`{|}~]/";s:7:"tagName";s:3:"ESC";}s:10:"FancyPants";a:2:{s:8:"attrName";s:4:"char";s:7:"tagName";s:2:"FP";}s:12:"HTMLComments";a:5:{s:10:"quickMatch";s:4:"<!--";s:11:"regexpLimit";i:50000;s:8:"attrName";s:7:"content";s:6:"regexp";s:22:"/<!--(?!\\[if).*?-->/is";s:7:"tagName";s:2:"HC";}s:12:"HTMLElements";a:5:{s:10:"quickMatch";s:1:"<";s:6:"prefix";s:4:"html";s:6:"regexp";s:385:"#<(?>/((?:a(?:bbr)?|br?|code|d(?:[dlt]|el|iv)|em|hr|i(?:mg|ns)?|li|ol|pre|r(?:[bp]|tc?|uby)|s(?:pan|trong|u[bp])?|t(?:[dr]|able|body|foot|h(?:ead)?)|ul?))|((?:a(?:bbr)?|br?|code|d(?:[dlt]|el|iv)|em|hr|i(?:mg|ns)?|li|ol|pre|r(?:[bp]|tc?|uby)|s(?:pan|trong|u[bp])?|t(?:[dr]|able|body|foot|h(?:ead)?)|ul?))((?>\\s+[a-z][-a-z0-9]*(?>\\s*=\\s*(?>"[^"]*"|\'[^\']*\'|[^\\s"\'=<>`]+))?)*+)\\s*/?)\\s*>#i";s:7:"aliases";a:6:{s:1:"a";a:2:{s:0:"";s:3:"URL";s:4:"href";s:3:"url";}s:2:"hr";a:1:{s:0:"";s:2:"HR";}s:2:"em";a:1:{s:0:"";s:2:"EM";}s:1:"s";a:1:{s:0:"";s:1:"S";}s:6:"strong";a:1:{s:0:"";s:6:"STRONG";}s:3:"sup";a:1:{s:0:"";s:3:"SUP";}}s:11:"regexpLimit";i:50000;}s:12:"HTMLEntities";a:5:{s:10:"quickMatch";s:1:"&";s:11:"regexpLimit";i:50000;s:8:"attrName";s:4:"char";s:6:"regexp";s:38:"/&(?>[a-z]+|#(?>[0-9]+|x[0-9a-f]+));/i";s:7:"tagName";s:2:"HE";}s:8:"Litedown";a:1:{s:18:"decodeHtmlEntities";b:1;}s:10:"MediaEmbed";a:4:{s:10:"quickMatch";s:3:"://";s:6:"regexp";s:26:"/\\bhttps?:\\/\\/[^["\'\\s]+/Si";s:7:"tagName";s:5:"MEDIA";s:11:"regexpLimit";i:50000;}s:10:"PipeTables";a:3:{s:16:"overwriteEscapes";b:1;s:17:"overwriteMarkdown";b:1;s:10:"quickMatch";s:1:"|";}}s:14:"registeredVars";a:3:{s:9:"urlConfig";a:1:{s:14:"allowedSchemes";s:27:"/^(?:ftp|https?|mailto)$/Di";}s:16:"MediaEmbed.hosts";a:17:{s:12:"bandcamp.com";s:8:"bandcamp";s:6:"dai.ly";s:11:"dailymotion";s:15:"dailymotion.com";s:11:"dailymotion";s:12:"facebook.com";s:8:"facebook";s:8:"fb.watch";s:8:"facebook";s:12:"liveleak.com";s:8:"liveleak";s:14:"soundcloud.com";s:10:"soundcloud";s:18:"link.tospotify.com";s:7:"spotify";s:16:"open.spotify.com";s:7:"spotify";s:16:"play.spotify.com";s:7:"spotify";s:12:"spotify.link";s:7:"spotify";s:9:"twitch.tv";s:6:"twitch";s:9:"vimeo.com";s:5:"vimeo";s:7:"vine.co";s:4:"vine";s:20:"youtube-nocookie.com";s:7:"youtube";s:11:"youtube.com";s:7:"youtube";s:8:"youtu.be";s:7:"youtube";}s:16:"MediaEmbed.sites";a:10:{s:8:"bandcamp";a:2:{i:0;a:0:{}i:1;a:2:{i:0;a:2:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:25:"!/album=(?\'album_id\'\\d+)!";i:1;a:2:{i:0;s:0:"";i:1;s:8:"album_id";}}}s:5:"match";a:1:{i:0;a:2:{i:0;s:23:"!bandcamp\\.com/album/.!";i:1;a:1:{i:0;s:0:"";}}}}i:1;a:2:{s:7:"extract";a:3:{i:0;a:2:{i:0;s:29:"!"album_id":(?\'album_id\'\\d+)!";i:1;R:94;}i:1;a:2:{i:0;s:31:"!"track_num":(?\'track_num\'\\d+)!";i:1;a:2:{i:0;s:0:"";i:1;s:9:"track_num";}}i:2;a:2:{i:0;s:25:"!/track=(?\'track_id\'\\d+)!";i:1;a:2:{i:0;s:0:"";i:1;s:8:"track_id";}}}s:5:"match";a:1:{i:0;a:2:{i:0;s:23:"!bandcamp\\.com/track/.!";i:1;R:100;}}}}}s:11:"dailymotion";a:2:{i:0;a:3:{i:0;a:2:{i:0;s:27:"!dai\\.ly/(?\'id\'[a-z0-9]+)!i";i:1;a:2:{i:0;s:0:"";i:1;s:2:"id";}}i:1;a:2:{i:0;s:92:"!dailymotion\\.com/(?:live/|swf/|user/[^#]+#video=|(?:related/\\d+/)?video/)(?\'id\'[a-z0-9]+)!i";i:1;R:123;}i:2;a:2:{i:0;s:17:"!start=(?\'t\'\\d+)!";i:1;a:2:{i:0;s:0:"";i:1;s:1:"t";}}}i:1;R:88;}s:8:"facebook";a:2:{i:0;a:10:{i:0;a:2:{i:0;s:55:"@facebook\\.com/.*?(?:fbid=|/permalink/|\\?v=)(?\'id\'\\d+)@";i:1;R:123;}i:1;a:2:{i:0;s:70:"@facebook\\.com/(?\'user\'[.\\w]+)/(?\'type\'[pv])(?:ost|ideo)s?/(?\'id\'\\d+)@";i:1;a:4:{i:0;s:0:"";i:1;s:4:"user";i:2;s:4:"type";i:3;s:2:"id";}}i:2;a:2:{i:0;s:49:"@facebook\\.com/video/(?=post|video)(?\'type\'[pv])@";i:1;a:2:{i:0;s:0:"";i:1;s:4:"type";}}i:3;a:2:{i:0;s:49:"@facebook\\.com/events/(?\'id\'\\d+)\\b(?!/permalink)@";i:1;R:123;}i:4;a:2:{i:0;s:38:"@facebook\\.com/watch/\\?(?\'type\'[pv])=@";i:1;R:146;}i:5;a:2:{i:0;s:53:"@facebook.com/groups/[^/]*/(?\'type\'p)osts/(?\'id\'\\d+)@";i:1;a:3:{i:0;s:0:"";i:1;s:4:"type";i:2;s:2:"id";}}i:6;a:2:{i:0;s:56:"@facebook\\.com/(?\'user\'[.\\w]+)/posts/pfbid(?\'pfbid\'\\w+)@";i:1;a:3:{i:0;s:0:"";i:1;s:4:"user";i:2;s:5:"pfbid";}}i:7;a:2:{i:0;s:95:"@facebook\\.com/permalink\\.php\\?story_fbid=(?:(?\'id\'\\d+)|pfbid(?\'pfbid\'\\w+))&id=(?\'page_id\'\\d+)@";i:1;a:4:{i:0;s:0:"";i:1;s:2:"id";i:2;s:5:"pfbid";i:3;s:7:"page_id";}}i:8;a:2:{i:0;s:65:"@facebook\\.com/(?\'user\'[.\\w]+)/(?\'type\'v)ideos/[^/]+/(?\'id\'\\d+)/@";i:1;R:139;}i:9;a:2:{i:0;s:40:"@facebook\\.com/(?\'type\'r)eel/(?\'id\'\\d+)@";i:1;R:155;}}i:1;a:3:{i:0;a:4:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:65:"@facebook\\.com/(?\'user\'[.\\w]+)/(?\'type\'[pv])\\w+/(?\'id\'\\d+)(?!\\w)@";i:1;R:139;}}s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:34:"@facebook\\.com/[.\\w]+/posts/pfbid@";i:1;R:100;}}s:3:"url";s:111:"https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2F{@user}%2Fposts%2Fpfbid{@pfbid}";}i:1;a:4:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:23:"@story_fbid=(?\'id\'\\d+)@";i:1;R:123;}}s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:80:"@facebook\\.com/permalink\\.php\\?story_fbid=pfbid(?\'pfbid\'\\w+)&id=(?\'page_id\'\\d+)@";i:1;a:3:{i:0;s:0:"";i:1;s:5:"pfbid";i:2;s:7:"page_id";}}}s:3:"url";s:140:"https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2Fpermalink.php%3Fstory_fbid%3Dpfbid{@pfbid}%26id%3D{@page_id}";}i:2;a:3:{s:7:"extract";a:2:{i:0;a:2:{i:0;s:45:"@facebook\\.com/watch/\\?(?\'type\'v)=(?\'id\'\\d+)@";i:1;R:155;}i:1;a:2:{i:0;s:58:"@facebook\\.com/(?\'user\'[.\\w]+)/(?\'type\'v)ideos/(?\'id\'\\d+)@";i:1;R:139;}}s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:13:"@fb\\.watch/.@";i:1;R:100;}}}}}s:8:"liveleak";a:2:{i:0;a:1:{i:0;a:2:{i:0;s:41:"!liveleak\\.com/(?:e/|view\\?i=)(?\'id\'\\w+)!";i:1;R:123;}}i:1;a:1:{i:0;a:2:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:28:"!liveleak\\.com/e/(?\'id\'\\w+)!";i:1;R:123;}}s:5:"match";a:1:{i:0;a:2:{i:0;s:24:"!liveleak\\.com/view\\?t=!";i:1;R:100;}}}}}s:10:"soundcloud";a:2:{i:0;a:4:{i:0;a:2:{i:0;s:84:"@https?://(?:api\\.)?soundcloud\\.com/(?!pages/)(?\'id\'[-/\\w]+/[-/\\w]+|^[^/]+/[^/]+$)@i";i:1;R:123;}i:1;a:2:{i:0;s:52:"@api\\.soundcloud\\.com/playlists/(?\'playlist_id\'\\d+)@";i:1;a:2:{i:0;s:0:"";i:1;s:11:"playlist_id";}}i:2;a:2:{i:0;s:89:"@api\\.soundcloud\\.com/tracks/(?\'track_id\'\\d+)(?:\\?secret_token=(?\'secret_token\'[-\\w]+))?@";i:1;a:3:{i:0;s:0:"";i:1;s:8:"track_id";i:2;s:12:"secret_token";}}i:3;a:2:{i:0;s:93:"@soundcloud\\.com/(?!playlists/|tracks/)[-\\w]+/(?:sets/)?[-\\w]+/(?=s-)(?\'secret_token\'[-\\w]+)@";i:1;a:2:{i:0;s:0:"";i:1;s:12:"secret_token";}}}i:1;a:2:{i:0;a:3:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:43:"@soundcloud(?::/)?:tracks:(?\'track_id\'\\d+)@";i:1;R:113;}}s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:56:"@soundcloud\\.com/(?!playlists/\\d|tracks/\\d)[-\\w]+/[-\\w]@";i:1;R:100;}}}i:1;a:3:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:49:"@soundcloud(?::/)?/playlists:(?\'playlist_id\'\\d+)@";i:1;R:227;}}s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:30:"@soundcloud\\.com/[-\\w]+/sets/@";i:1;R:100;}}}}}s:7:"spotify";a:2:{i:0;a:1:{i:0;a:2:{i:0;s:125:"!(?:open|play)\\.spotify\\.com/(?:intl-\\w+/|user/[-.\\w]+/)*(?\'id\'(?:album|artist|episode|playlist|show|track)(?:[:/][-.\\w]+)+)!";i:1;R:123;}}i:1;a:1:{i:0;a:3:{s:7:"extract";R:259;s:6:"header";s:29:"User-agent: PHP (not Mozilla)";s:5:"match";a:1:{i:0;a:2:{i:0;s:51:"!https?://(?:link\\.tospotify\\.com|spotify\\.link)/.!";i:1;R:100;}}}}}s:6:"twitch";a:2:{i:0;a:4:{i:0;a:2:{i:0;s:47:"#twitch\\.tv/(?:videos|\\w+/v)/(?\'video_id\'\\d+)?#";i:1;a:2:{i:0;s:0:"";i:1;s:8:"video_id";}}i:1;a:2:{i:0;s:73:"#www\\.twitch\\.tv/(?!videos/)(?\'channel\'\\w+)(?:/clip/(?\'clip_id\'[-\\w]+))?#";i:1;a:3:{i:0;s:0:"";i:1;s:7:"channel";i:2;s:7:"clip_id";}}i:2;a:2:{i:0;s:32:"#t=(?\'t\'(?:(?:\\d+h)?\\d+m)?\\d+s)#";i:1;R:130;}i:3;a:2:{i:0;s:59:"#clips\\.twitch\\.tv/(?:(?\'channel\'\\w+)/)?(?\'clip_id\'[-\\w]+)#";i:1;R:277;}}i:1;R:88;}s:5:"vimeo";a:2:{i:0;a:2:{i:0;a:2:{i:0;s:67:"!vimeo\\.com/(?:channels/[^/]+/|video/)?(?\'id\'\\d+)(?:/(?\'h\'\\w+))?\\b!";i:1;a:3:{i:0;s:0:"";i:1;s:2:"id";i:2;s:1:"h";}}i:1;a:2:{i:0;s:19:"!#t=(?\'t\'[\\dhms]+)!";i:1;R:130;}}i:1;R:88;}s:4:"vine";a:2:{i:0;a:1:{i:0;a:2:{i:0;s:25:"!vine\\.co/v/(?\'id\'[^/]+)!";i:1;R:123;}}i:1;R:88;}s:7:"youtube";a:2:{i:0;a:5:{i:0;a:2:{i:0;s:91:"!youtube\\.com/(?:watch.*?v=|(?:embed|live|shorts|v)/|attribution_link.*?v%3D)(?\'id\'[-\\w]+)!";i:1;R:123;}i:1;a:2:{i:0;s:43:"!youtube-nocookie\\.com/embed/(?\'id\'[-\\w]+)!";i:1;R:123;}i:2;a:2:{i:0;s:25:"!youtu\\.be/(?\'id\'[-\\w]+)!";i:1;R:123;}i:3;a:2:{i:0;s:42:"@[#&?]t(?:ime_continue)?=(?\'t\'\\d[\\dhms]*)@";i:1;R:130;}i:4;a:2:{i:0;s:26:"![&?]list=(?\'list\'[-\\w]+)!";i:1;a:2:{i:0;s:0:"";i:1;s:4:"list";}}}i:1;a:1:{i:0;a:2:{s:7:"extract";a:1:{i:0;a:2:{i:0;s:71:"@/embed/(?\'id\'[-\\w]+)\\?clip=(?\'clip\'[-\\w]+)&amp;clipt=(?\'clipt\'[-\\w]+)@";i:1;a:4:{i:0;s:0:"";i:1;s:2:"id";i:2;s:4:"clip";i:3;s:5:"clipt";}}}s:5:"match";a:1:{i:0;a:2:{i:0;s:21:"@youtube\\.com/clip/.@";i:1;R:100;}}}}}}}s:14:"' . "\0" . '*' . "\0" . 'rootContext";a:2:{s:7:"allowed";a:2:{i:0;i:65519;i:1;i:65457;}s:5:"flags";i:8;}s:13:"' . "\0" . '*' . "\0" . 'tagsConfig";a:77:{s:8:"BANDCAMP";a:7:{s:10:"attributes";a:3:{s:8:"album_id";a:2:{s:8:"required";b:0;s:11:"filterChain";R:88;}s:8:"track_id";R:335;s:9:"track_num";R:335;}s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:59:"s9e\\TextFormatter\\Parser\\FilterProcessing::filterAttributes";s:6:"params";a:4:{s:3:"tag";N;s:9:"tagConfig";N;s:14:"registeredVars";N;s:6:"logger";N;}}}s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:3089;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";a:2:{i:0;i:32960;i:1;i:33025;}}s:1:"C";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:66;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";a:2:{i:0;i:0;i:1;i:0;}}s:4:"CODE";a:7:{s:10:"attributes";a:1:{s:4:"lang";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:62:"s9e\\TextFormatter\\Parser\\AttributeFilters\\RegexpFilter::filter";s:6:"params";a:2:{s:9:"attrValue";N;i:0;s:23:"/^[- +,.0-9A-Za-z_]+$/D";}}}s:8:"required";b:0;}}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:10:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:4436;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:1;s:7:"allowed";R:359;}s:11:"DAILYMOTION";a:7:{s:10:"attributes";a:2:{s:2:"id";R:335;s:1:"t";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:3:"DEL";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:512;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";R:328;}s:2:"EM";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:2;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";a:2:{i:0;i:65477;i:1;i:65409;}}s:5:"EMAIL";a:7:{s:10:"attributes";a:1:{s:5:"email";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:61:"s9e\\TextFormatter\\Parser\\AttributeFilters\\EmailFilter::filter";s:6:"params";a:1:{s:9:"attrValue";N;}}}s:8:"required";b:1;}}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:514;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";a:2:{i:0;i:39819;i:1;i:65457;}}s:3:"ESC";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:1616;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:7;s:7:"allowed";R:359;}s:8:"FACEBOOK";a:7:{s:10:"attributes";a:5:{s:2:"id";R:335;s:7:"page_id";R:335;s:5:"pfbid";R:335;s:4:"type";R:335;s:4:"user";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:2:"FP";a:7:{s:10:"attributes";a:1:{s:4:"char";a:2:{s:8:"required";b:1;s:11:"filterChain";R:88;}}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:8;s:7:"allowed";a:2:{i:0;i:32896;i:1;i:33153;}}s:2:"H1";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:374;s:12:"fosterParent";R:374;s:5:"flags";i:260;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:3;s:7:"allowed";R:405;}s:2:"H2";R:446;s:2:"H3";R:446;s:2:"H4";R:446;s:2:"H5";R:446;s:2:"H6";R:446;s:2:"HC";a:7:{s:10:"attributes";a:1:{s:7:"content";R:438;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:3153;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:7;s:7:"allowed";R:359;}s:2:"HE";R:436;s:2:"HR";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:2:{s:11:"closeParent";R:374;s:5:"flags";i:3349;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";R:443;}s:3:"IMG";a:7:{s:10:"attributes";a:3:{s:3:"alt";R:335;s:3:"src";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:59:"s9e\\TextFormatter\\Parser\\AttributeFilters\\UrlFilter::filter";s:6:"params";a:3:{s:9:"attrValue";N;s:9:"urlConfig";N;s:6:"logger";N;}}}s:8:"required";b:1;}s:5:"title";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:0;s:7:"allowed";R:443;}s:8:"ISPOILER";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:0;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";R:405;}s:2:"LI";a:7:{s:11:"filterChain";a:2:{i:0;R:338;i:1;a:2:{s:8:"callback";s:58:"s9e\\TextFormatter\\Plugins\\TaskLists\\Helper::filterListItem";s:6:"params";a:3:{s:6:"parser";N;s:3:"tag";N;s:4:"text";N;}}}s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:12:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:2:"LI";i:1;s:7:"html:li";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:264;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:4;s:7:"allowed";a:2:{i:0;i:65519;i:1;i:65441;}}s:4:"LIST";a:7:{s:10:"attributes";a:2:{s:5:"start";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:67:"s9e\\TextFormatter\\Parser\\AttributeFilters\\NumericFilter::filterUint";s:6:"params";R:414;}}s:8:"required";b:0;}s:4:"type";R:364;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:374;s:12:"fosterParent";R:374;s:5:"flags";i:3460;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:1;s:7:"allowed";a:2:{i:0;i:65424;i:1;i:65408;}}s:8:"LIVELEAK";a:7:{s:10:"attributes";a:1:{s:2:"id";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:5:"MEDIA";a:7:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:54:"s9e\\TextFormatter\\Plugins\\MediaEmbed\\Parser::filterTag";s:6:"params";a:5:{s:3:"tag";N;s:6:"parser";N;s:16:"MediaEmbed.hosts";N;s:16:"MediaEmbed.sites";N;s:8:"cacheDir";N;}}}s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:513;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:15;s:7:"allowed";a:2:{i:0;i:65519;i:1;i:65329;}}s:5:"QUOTE";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:374;s:12:"fosterParent";R:374;s:5:"flags";i:268;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";R:511;}s:10:"SOUNDCLOUD";a:7:{s:10:"attributes";a:4:{s:2:"id";R:335;s:11:"playlist_id";R:335;s:12:"secret_token";R:335;s:8:"track_id";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:7:"SPOILER";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:554;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:5;s:7:"allowed";R:511;}s:7:"SPOTIFY";R:529;s:6:"STRONG";R:399;s:3:"SUB";R:479;s:3:"SUP";R:479;s:5:"TABLE";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:522;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";a:2:{i:0;i:65408;i:1;i:65418;}}s:4:"TASK";a:7:{s:10:"attributes";a:2:{s:2:"id";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:62:"s9e\\TextFormatter\\Parser\\AttributeFilters\\RegexpFilter::filter";s:6:"params";a:2:{s:9:"attrValue";N;i:0;s:19:"/^[-0-9A-Za-z_]+$/D";}}}s:8:"required";b:1;}s:5:"state";R:576;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:443;}s:5:"TBODY";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:20:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:5:"TBODY";i:1;s:2:"TD";i:1;s:2:"TH";i:1;s:5:"THEAD";i:1;s:2:"TR";i:1;s:10:"html:tbody";i:1;s:7:"html:td";i:1;s:7:"html:th";i:1;s:10:"html:thead";i:1;s:7:"html:tr";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:3456;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:9;s:7:"allowed";a:2:{i:0;i:65408;i:1;i:65416;}}s:2:"TD";a:7:{s:10:"attributes";a:1:{s:5:"align";a:2:{s:11:"filterChain";a:2:{i:0;a:2:{s:8:"callback";s:10:"strtolower";s:6:"params";R:414;}i:1;a:2:{s:8:"callback";s:62:"s9e\\TextFormatter\\Parser\\AttributeFilters\\RegexpFilter::filter";s:6:"params";a:2:{s:9:"attrValue";N;i:0;s:34:"/^(?:center|justify|left|right)$/D";}}}s:8:"required";b:0;}}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:14:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:2:"TD";i:1;s:2:"TH";i:1;s:7:"html:td";i:1;s:7:"html:th";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:256;}s:8:"tagLimit";i:5000;s:9:"bitNumber";i:10;s:7:"allowed";R:511;}s:2:"TH";a:7:{s:10:"attributes";R:618;s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:630;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:10;s:7:"allowed";a:2:{i:0;i:63463;i:1;i:65441;}}s:5:"THEAD";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:374;s:12:"fosterParent";R:374;s:5:"flags";i:3456;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:9;s:7:"allowed";R:614;}s:2:"TR";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:16:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:2:"TD";i:1;s:2:"TH";i:1;s:2:"TR";i:1;s:7:"html:td";i:1;s:7:"html:th";i:1;s:7:"html:tr";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:3456;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:11;s:7:"allowed";a:2:{i:0;i:65408;i:1;i:65412;}}s:6:"TWITCH";a:7:{s:10:"attributes";a:4:{s:7:"channel";R:335;s:7:"clip_id";R:335;s:1:"t";R:335;s:8:"video_id";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:3:"URL";a:7:{s:10:"attributes";a:2:{s:5:"title";R:335;s:3:"url";R:467;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:418;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:6;s:7:"allowed";R:422;}s:5:"VIMEO";a:7:{s:10:"attributes";a:3:{s:1:"h";R:335;s:2:"id";R:335;s:1:"t";a:2:{s:11:"filterChain";a:1:{i:0;a:2:{s:8:"callback";s:65:"s9e\\TextFormatter\\Parser\\AttributeFilters\\TimestampFilter::filter";s:6:"params";R:414;}}s:8:"required";b:0;}}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:4:"VINE";R:529;s:7:"YOUTUBE";a:7:{s:10:"attributes";a:5:{s:4:"clip";R:335;s:5:"clipt";R:335;s:2:"id";a:2:{s:11:"filterChain";R:577;s:8:"required";b:0;}s:4:"list";R:335;s:1:"t";R:700;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:346;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:2;s:7:"allowed";R:350;}s:9:"html:abbr";a:7:{s:10:"attributes";a:1:{s:5:"title";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:481;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:0;s:7:"allowed";R:405;}s:6:"html:b";R:399;s:7:"html:br";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:1:{s:5:"flags";i:3201;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";a:2:{i:0;i:65408;i:1;i:65408;}}s:9:"html:code";R:353;s:7:"html:dd";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:12:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:7:"html:dd";i:1;s:7:"html:dt";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:256;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:12;s:7:"allowed";R:511;}s:8:"html:del";R:393;s:8:"html:div";a:7:{s:10:"attributes";a:1:{s:5:"class";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:554;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:13;s:7:"allowed";R:328;}s:7:"html:dl";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:522;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";a:2:{i:0;i:65408;i:1;i:65456;}}s:7:"html:dt";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:731;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:12;s:7:"allowed";R:653;}s:6:"html:i";R:399;s:8:"html:img";a:7:{s:10:"attributes";a:5:{s:3:"alt";R:335;s:6:"height";R:335;s:3:"src";a:2:{s:11:"filterChain";R:468;s:8:"required";b:0;}s:5:"title";R:335;s:5:"width";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:722;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:0;s:7:"allowed";R:726;}s:8:"html:ins";R:393;s:7:"html:li";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:494;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:4;s:7:"allowed";R:511;}s:7:"html:ol";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:522;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";R:526;}s:8:"html:pre";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:374;s:12:"fosterParent";R:374;s:5:"flags";i:276;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:1;s:7:"allowed";R:405;}s:7:"html:rb";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:658;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:14;s:7:"allowed";R:726;}s:7:"html:rp";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";a:12:{s:1:"C";i:1;s:2:"EM";i:1;s:5:"EMAIL";i:1;s:6:"STRONG";i:1;s:3:"URL";i:1;s:6:"html:b";i:1;s:9:"html:code";i:1;s:6:"html:i";i:1;s:11:"html:strong";i:1;s:6:"html:u";i:1;s:7:"html:rp";i:1;s:7:"html:rt";i:1;}s:12:"fosterParent";R:374;s:5:"flags";i:3344;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:14;s:7:"allowed";R:443;}s:7:"html:rt";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";a:3:{s:11:"closeParent";R:792;s:12:"fosterParent";R:374;s:5:"flags";i:256;}s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:14;s:7:"allowed";R:405;}s:8:"html:rtc";R:785;s:9:"html:ruby";a:7:{s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:481;s:8:"tagLimit";i:5000;s:10:"attributes";R:88;s:9:"bitNumber";i:0;s:7:"allowed";a:2:{i:0;i:65477;i:1;i:65473;}}s:9:"html:span";a:7:{s:10:"attributes";R:749;s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:481;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:0;s:7:"allowed";R:405;}s:11:"html:strong";R:399;s:8:"html:sub";R:479;s:8:"html:sup";R:479;s:10:"html:table";R:567;s:10:"html:tbody";R:587;s:7:"html:td";a:7:{s:10:"attributes";a:2:{s:7:"colspan";R:335;s:7:"rowspan";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:630;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:10;s:7:"allowed";R:511;}s:10:"html:tfoot";R:587;s:7:"html:th";a:7:{s:10:"attributes";a:3:{s:7:"colspan";R:335;s:7:"rowspan";R:335;s:5:"scope";R:335;}s:11:"filterChain";R:337;s:12:"nestingLimit";i:10;s:5:"rules";R:630;s:8:"tagLimit";i:5000;s:9:"bitNumber";i:10;s:7:"allowed";R:653;}s:10:"html:thead";R:656;s:7:"html:tr";R:662;s:6:"html:u";R:399;s:7:"html:ul";R:775;}}');
	}

	/**
	* {@inheritdoc}
	*/
	public static function getRenderer()
	{
		return unserialize('O:42:"s9e\\TextFormatter\\Bundles\\Fatdown\\Renderer":2:{s:19:"enableQuickRenderer";b:1;s:9:"' . "\0" . '*' . "\0" . 'params";a:2:{s:16:"MEDIAEMBED_THEME";s:0:"";s:18:"TASKLISTS_EDITABLE";s:0:"";}}');
	}
}