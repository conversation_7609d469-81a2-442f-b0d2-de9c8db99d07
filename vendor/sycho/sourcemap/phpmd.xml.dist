<?xml version="1.0"?> 
<ruleset 
    name="Axypro rule set" 
    xmlns="http://pmd.sf.net/ruleset/1.0.0" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd" 
    xsi:noNamespaceSchemaLocation=" http://pmd.sf.net/ruleset_xml_schema.xsd"> 
    
    <rule ref="rulesets/cleancode.xml">
        <exclude name="StaticAccess" />
        <exclude name="ElseExpression" />
        <exclude name="BooleanArgumentFlag" />
    </rule>
    <rule ref="rulesets/controversial.xml" />
    <rule ref="rulesets/design.xml" />
    <rule ref="rulesets/naming.xml">
        <exclude name="ShortVariable" />
    </rule>
    <rule ref="rulesets/unusedcode.xml" />
</ruleset>
