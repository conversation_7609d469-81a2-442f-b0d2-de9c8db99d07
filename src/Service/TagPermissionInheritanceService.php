<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Service;

use Flarum\Tags\Tag;
use Flarum\User\User;

class TagPermissionInheritanceService
{
    /**
     * Check if a user can perform an ability on a tag with inheritance logic
     * 
     * @param User $actor
     * @param string $ability
     * @param Tag $tag
     * @return bool
     */
    public function canWithInheritance(User $actor, string $ability, Tag $tag): bool
    {
        // Admin users have all permissions
        if ($actor->isAdmin()) {
            return true;
        }

        // Check if the tag has explicit permissions
        if ($tag->is_restricted) {
            $id = $tag->id;

            // Check direct permission on this tag
            if ($actor->hasPermission("tag$id.$ability")) {
                return true;
            }
        }

        // Check parent permissions recursively for inheritance
        if ($tag->parent_id !== null && $tag->parent) {
            $parentResult = $this->checkParentPermissions($actor, $ability, $tag->parent);
            if ($parentResult === true) {
                return true;
            }
            if ($parentResult === false) {
                return false;
            }
            // If parentResult is null, continue checking
        }

        // If tag is not restricted, check global permissions
        if (!$tag->is_restricted) {
            return $actor->hasPermission($ability);
        }

        // If tag is restricted but user has no permission and no parent inheritance, deny
        return false;
    }

    /**
     * Recursively check parent tag permissions
     * Returns: true = allow, false = deny, null = continue checking
     * 
     * @param User $actor
     * @param string $ability
     * @param Tag $parentTag
     * @return bool|null
     */
    protected function checkParentPermissions(User $actor, string $ability, Tag $parentTag): ?bool
    {
        // Check if parent has explicit permissions
        if ($parentTag->is_restricted) {
            $parentId = $parentTag->id;

            if ($actor->hasPermission("tag$parentId.$ability")) {
                return true; // Allow access
            }
        }

        // If parent has a parent, check recursively
        if ($parentTag->parent_id !== null && $parentTag->parent) {
            return $this->checkParentPermissions($actor, $ability, $parentTag->parent);
        }

        // If parent is not restricted, check global permissions
        if (!$parentTag->is_restricted) {
            return $actor->hasPermission($ability) ? true : null;
        }

        // If parent is restricted but user has no permission, deny access
        return false;
    }
}
