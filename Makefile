DOCKER_COMPOSE = docker compose -f ../../flarum/docker-compose.yml
CURRENT_DIR_NAME := $(shell basename $(shell pwd))

# Default target
.PHONY: default
default: help

# Default target: list all available targets
.PHONY: help
help: ## Display this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Run bash in PHP container
.PHONY: sh
sh: ## Connect to PHP container
	@$(DOCKER_COMPOSE) exec -w /var/www/app/packages/$(CURRENT_DIR_NAME) php bash

# Build JS
.PHONY: build-js
build-js: ## Build JS
	@$(DOCKER_COMPOSE) exec -w /var/www/app/packages/$(CURRENT_DIR_NAME)/js php npm run build

# Check JS
.PHONY: check-js
check-js: ## Check JS
	@$(DOCKER_COMPOSE) exec -w /var/www/app/packages/$(CURRENT_DIR_NAME)/js php npm run format
	@$(DOCKER_COMPOSE) exec -w /var/www/app/packages/$(CURRENT_DIR_NAME)/js php npm run format-check
	@$(DOCKER_COMPOSE) exec -w /var/www/app/packages/$(CURRENT_DIR_NAME)/js php npm run check-typings
